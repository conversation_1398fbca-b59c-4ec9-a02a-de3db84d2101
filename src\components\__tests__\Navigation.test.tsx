/**
 * Navigation 组件测试
 */

import { render, screen, fireEvent, waitFor } from '@/lib/test-utils'
import Navigation from '../Navigation'
import { AuthProvider } from '@/contexts/AuthContext'
import { mockUser } from '@/lib/test-utils'

// 模拟 Next.js 路由
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/'
  }),
  usePathname: () => '/'
}))

// 模拟 Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: null },
        error: null
      }),
      signOut: jest.fn().mockResolvedValue({ error: null })
    }
  }
}))

describe('Navigation', () => {
  it('should render navigation links', () => {
    render(<Navigation />)
    
    expect(screen.getByText('Magic Academy')).toBeInTheDocument()
    expect(screen.getByText('Tutorials')).toBeInTheDocument()
    expect(screen.getByText('About')).toBeInTheDocument()
    expect(screen.getByText('Contact')).toBeInTheDocument()
  })

  it('should show login/register buttons when user is not authenticated', () => {
    render(<Navigation />)
    
    expect(screen.getByText('Login')).toBeInTheDocument()
    expect(screen.getByText('Register')).toBeInTheDocument()
  })

  it('should show user menu when user is authenticated', async () => {
    // 模拟已认证用户
    const mockAuthContext = {
      user: mockUser,
      isAuthenticated: true,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
      loading: false,
      hasAccess: jest.fn().mockReturnValue(true)
    }

    // 这里需要创建一个自定义的 AuthProvider 来提供模拟数据
    const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider value={mockAuthContext}>
        {children}
      </AuthProvider>
    )

    render(<Navigation />, { wrapper: MockAuthProvider })
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Library')).toBeInTheDocument()
    expect(screen.getByText('Logout')).toBeInTheDocument()
  })

  it('should toggle mobile menu', () => {
    render(<Navigation />)
    
    const mobileMenuButton = screen.getByRole('button', { name: /menu/i })
    
    // 初始状态下移动菜单应该是隐藏的
    expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
    
    // 点击按钮显示移动菜单
    fireEvent.click(mobileMenuButton)
    expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
    
    // 再次点击隐藏移动菜单
    fireEvent.click(mobileMenuButton)
    expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
  })

  it('should handle logout', async () => {
    const mockLogout = jest.fn()
    
    const mockAuthContext = {
      user: mockUser,
      isAuthenticated: true,
      login: jest.fn(),
      logout: mockLogout,
      register: jest.fn(),
      loading: false,
      hasAccess: jest.fn().mockReturnValue(true)
    }

    const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider value={mockAuthContext}>
        {children}
      </AuthProvider>
    )

    render(<Navigation />, { wrapper: MockAuthProvider })
    
    const logoutButton = await screen.findByText('Logout')
    fireEvent.click(logoutButton)
    
    expect(mockLogout).toHaveBeenCalled()
  })

  it('should highlight active navigation item', () => {
    // 模拟当前路径为 /tutorials
    jest.mocked(require('next/navigation').usePathname).mockReturnValue('/tutorials')
    
    render(<Navigation />)
    
    const tutorialsLink = screen.getByText('Tutorials')
    expect(tutorialsLink.closest('a')).toHaveClass('active') // 假设有 active 类
  })

  it('should be accessible', () => {
    render(<Navigation />)
    
    // 检查导航是否有正确的 ARIA 标签
    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()
    
    // 检查链接是否可访问
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAttribute('href')
    })
    
    // 检查按钮是否可访问
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toBeEnabled()
    })
  })

  it('should handle keyboard navigation', () => {
    render(<Navigation />)
    
    const firstLink = screen.getByText('Magic Academy')
    firstLink.focus()
    
    expect(document.activeElement).toBe(firstLink)
    
    // 测试 Tab 键导航
    fireEvent.keyDown(firstLink, { key: 'Tab' })
    // 这里可以添加更多键盘导航测试
  })

  it('should render correctly on different screen sizes', () => {
    // 测试响应式设计
    const { rerender } = render(<Navigation />)
    
    // 模拟移动设备
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })
    
    rerender(<Navigation />)
    
    // 检查移动菜单按钮是否存在
    expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument()
    
    // 模拟桌面设备
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    })
    
    rerender(<Navigation />)
    
    // 检查桌面导航是否正确显示
    expect(screen.getByText('Tutorials')).toBeVisible()
  })

  it('should handle loading state', () => {
    const mockAuthContext = {
      user: null,
      isAuthenticated: false,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
      loading: true, // 设置为加载状态
      hasAccess: jest.fn().mockReturnValue(false)
    }

    const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider value={mockAuthContext}>
        {children}
      </AuthProvider>
    )

    render(<Navigation />, { wrapper: MockAuthProvider })
    
    // 在加载状态下，用户相关的按钮应该显示加载指示器或被禁用
    // 这取决于具体的实现
    expect(screen.queryByText('Login')).toBeInTheDocument()
  })

  it('should handle authentication errors gracefully', async () => {
    const mockAuthContext = {
      user: null,
      isAuthenticated: false,
      login: jest.fn().mockRejectedValue(new Error('Authentication failed')),
      logout: jest.fn(),
      register: jest.fn(),
      loading: false,
      hasAccess: jest.fn().mockReturnValue(false)
    }

    const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider value={mockAuthContext}>
        {children}
      </AuthProvider>
    )

    render(<Navigation />, { wrapper: MockAuthProvider })
    
    // 这里可以测试错误处理逻辑
    // 例如，如果有错误消息显示组件
  })
})
