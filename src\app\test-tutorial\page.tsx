'use client'

import { useAuth, useTutorialAccess } from '@/contexts/AuthContext'
import Navigation from '@/components/Navigation'
import Link from 'next/link'

export default function TestTutorialPage() {
  const { user, loading: authLoading } = useAuth()
  const { hasAccess, loading: accessLoading } = useTutorialAccess('1')

  if (authLoading || accessLoading) {
    return (
      <div className="min-h-screen bg-cream-50">
        <Navigation />
        <div className="pt-32 container mx-auto px-4 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50">
      <Navigation />
      <div className="pt-32 container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Test Tutorial Page</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <p><strong>User:</strong> {user ? `${user.name} (${user.email})` : 'Not logged in'}</p>
          <p><strong>Has Access to Tutorial 1:</strong> {hasAccess === null ? 'Unknown' : hasAccess ? 'Yes' : 'No'}</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Tutorial: The Classical Card Force</h2>
          <p className="mb-4">Price: $49.99</p>
          
          <div className="space-y-4">
            {user ? (
              hasAccess ? (
                <Link href="/watch/1" className="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700">
                  Watch Tutorial
                </Link>
              ) : (
                <Link href="/purchase/1" className="inline-block bg-burgundy-600 text-white px-6 py-3 rounded-lg hover:bg-burgundy-700">
                  Purchase Tutorial
                </Link>
              )
            ) : (
              <Link href="/auth/login" className="inline-block bg-burgundy-600 text-white px-6 py-3 rounded-lg hover:bg-burgundy-700">
                Sign In to Purchase
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
