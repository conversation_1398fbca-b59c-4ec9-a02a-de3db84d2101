import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function debugDatabase() {
  try {
    console.log('🔍 Debugging database state...\n')

    // Check tutorials
    console.log('📚 Tutorials in database:')
    const { data: tutorials, error: tutorialsError } = await supabase
      .from('tutorials')
      .select('*')
    
    if (tutorialsError) {
      console.error('Error fetching tutorials:', tutorialsError)
    } else {
      tutorials?.forEach(tutorial => {
        console.log(`- ID: ${tutorial.id}, Title: ${tutorial.title}`)
      })
    }

    // Check users
    console.log('\n👤 Users in auth:')
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      console.error('Error fetching users:', usersError)
    } else {
      users.users.forEach(user => {
        console.log(`- ID: ${user.id}, Email: ${user.email}`)
      })
    }

    // Check purchases
    console.log('\n🛒 Purchases in database:')
    const { data: purchases, error: purchasesError } = await supabase
      .from('purchases')
      .select('*')
    
    if (purchasesError) {
      console.error('Error fetching purchases:', purchasesError)
    } else {
      if (purchases && purchases.length > 0) {
        purchases.forEach(purchase => {
          console.log(`- ID: ${purchase.id}, User: ${purchase.user_id}, Tutorial: ${purchase.tutorial_id}, Status: ${purchase.status}`)
        })
      } else {
        console.log('No purchases found')
      }
    }

    // Check profiles
    console.log('\n👥 Profiles in database:')
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError)
    } else {
      if (profiles && profiles.length > 0) {
        profiles.forEach(profile => {
          console.log(`- ID: ${profile.id}, Email: ${profile.email}, Name: ${profile.name}`)
        })
      } else {
        console.log('No profiles found')
      }
    }

  } catch (error) {
    console.error('❌ Error debugging database:', error)
  }
}

debugDatabase()
