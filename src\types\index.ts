// User types - 区分 Supabase User 和应用 User
export interface AppUser {
  id: string
  email: string
  name?: string  // 改为可选，因为 Supabase 用户可能没有 name
  createdAt?: Date
  updatedAt?: Date
}

export interface AuthUser extends AppUser {
  isAuthenticated: boolean
}

// 兼容性别名，逐步迁移
export interface User extends AppUser {}

// Product/Tutorial types
export interface Tutorial {
  id: string
  title: string
  description: string
  price: number
  featured: boolean
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  duration: number // in seconds
  coverImage?: string
  preview_video_url?: string
  videos: Video[]
  createdAt?: Date
  updatedAt?: Date
}

export interface Video {
  id: string
  tutorialId: string
  title: string
  description?: string
  duration: number
  videoUrl: string
  thumbnailUrl?: string
  order: number
  createdAt?: Date
  updatedAt?: Date
}

// Purchase types
export interface Purchase {
  id: string
  userId: string
  tutorialId: string
  amount: number
  paypalOrderId: string
  status: 'pending' | 'completed' | 'failed'
  createdAt?: Date
  updatedAt?: Date
}

// Blog types
export interface BlogPost {
  id: string
  title: string
  content: string
  excerpt: string
  featured: boolean
  published: boolean
  author: string
  tags: string[]
  createdAt?: Date
  updatedAt?: Date
}

// Portfolio types
export interface PortfolioItem {
  id: string
  title: string
  description: string
  imageUrl: string
  category: string
  featured: boolean
  date: string
  createdAt?: Date
  updatedAt?: Date
}

// Payment types
export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: string
  clientSecret?: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  name?: string
}

// Database types for SQLite
export interface DbUser {
  id: string
  email: string
  password_hash: string
  name: string | null
  created_at: string
  updated_at: string
}

export interface DbTutorial {
  id: string
  title: string
  description: string
  price: number
  cover_image: string | null
  created_at: string
  updated_at: string
}

export interface DbVideo {
  id: string
  title: string
  description: string | null
  duration: number
  video_url: string
  thumbnail_url: string | null
  order_index: number
  tutorial_id: string
}

export interface DbPurchase {
  id: string
  user_id: string
  tutorial_id: string
  amount: number
  payment_id: string
  status: string
  created_at: string
}
