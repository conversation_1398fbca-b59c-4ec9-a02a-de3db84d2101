/**
 * 验证工具测试
 */

import {
  createValidator,
  validateTutorialData,
  validateLoginData,
  validateRegistrationData,
  SecurityUtils
} from '../validation'

describe('Validator', () => {
  describe('email validation', () => {
    it('should validate correct email addresses', () => {
      const validator = createValidator()
      const result = validator.email('<EMAIL>').getResult()
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject invalid email addresses', () => {
      const validator = createValidator()
      const result = validator.email('invalid-email').getResult()
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Email format is invalid')
    })

    it('should reject empty email', () => {
      const validator = createValidator()
      const result = validator.email('').getResult()
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Email is required')
    })
  })

  describe('password validation', () => {
    it('should validate strong passwords', () => {
      const validator = createValidator()
      const result = validator.password('strongpassword123').getResult()
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject short passwords', () => {
      const validator = createValidator()
      const result = validator.password('short').getResult()
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must be at least 8 characters long')
    })
  })

  describe('price validation', () => {
    it('should validate positive numbers', () => {
      const validator = createValidator()
      const result = validator.price(29.99).getResult()
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject negative numbers', () => {
      const validator = createValidator()
      const result = validator.price(-10).getResult()
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Price must be a valid positive number')
    })

    it('should reject non-numeric values', () => {
      const validator = createValidator()
      const result = validator.price('not-a-number').getResult()
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Price must be a valid positive number')
    })
  })
})

describe('validateTutorialData', () => {
  const validTutorialData = {
    title: 'Magic Card Tricks',
    description: 'Learn amazing card tricks that will amaze your audience',
    price: 29.99,
    difficulty: 'beginner'
  }

  it('should validate correct tutorial data', () => {
    const result = validateTutorialData(validTutorialData)
    
    expect(result.isValid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })

  it('should reject tutorial without title', () => {
    const invalidData = { ...validTutorialData, title: '' }
    const result = validateTutorialData(invalidData)
    
    expect(result.isValid).toBe(false)
    expect(result.errors).toContain('Title is required')
  })

  it('should reject tutorial with invalid difficulty', () => {
    const invalidData = { ...validTutorialData, difficulty: 'invalid' }
    const result = validateTutorialData(invalidData)
    
    expect(result.isValid).toBe(false)
    expect(result.errors).toContain('Difficulty must be one of: beginner, intermediate, advanced')
  })

  it('should reject tutorial with negative price', () => {
    const invalidData = { ...validTutorialData, price: -10 }
    const result = validateTutorialData(invalidData)
    
    expect(result.isValid).toBe(false)
    expect(result.errors).toContain('Price must be a valid positive number')
  })
})

describe('validateLoginData', () => {
  const validLoginData = {
    email: '<EMAIL>',
    password: 'password123'
  }

  it('should validate correct login data', () => {
    const result = validateLoginData(validLoginData)
    
    expect(result.isValid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })

  it('should reject login without email', () => {
    const invalidData = { ...validLoginData, email: '' }
    const result = validateLoginData(invalidData)
    
    expect(result.isValid).toBe(false)
    expect(result.errors).toContain('Email is required')
  })

  it('should reject login without password', () => {
    const invalidData = { ...validLoginData, password: '' }
    const result = validateLoginData(invalidData)
    
    expect(result.isValid).toBe(false)
    expect(result.errors).toContain('Password is required')
  })
})

describe('validateRegistrationData', () => {
  const validRegistrationData = {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User'
  }

  it('should validate correct registration data', () => {
    const result = validateRegistrationData(validRegistrationData)
    
    expect(result.isValid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })

  it('should validate registration data without name', () => {
    const dataWithoutName = { ...validRegistrationData, name: undefined }
    const result = validateRegistrationData(dataWithoutName)
    
    expect(result.isValid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })

  it('should reject registration with invalid email', () => {
    const invalidData = { ...validRegistrationData, email: 'invalid-email' }
    const result = validateRegistrationData(invalidData)
    
    expect(result.isValid).toBe(false)
    expect(result.errors).toContain('Email format is invalid')
  })
})

describe('SecurityUtils', () => {
  describe('sanitizeInput', () => {
    it('should remove dangerous HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello World'
      const result = SecurityUtils.sanitizeInput(input)
      
      expect(result).toBe('scriptalert("xss")/scriptHello World')
      expect(result).not.toContain('<script>')
    })

    it('should remove javascript protocols', () => {
      const input = 'javascript:alert("xss")'
      const result = SecurityUtils.sanitizeInput(input)
      
      expect(result).toBe('alert("xss")')
      expect(result).not.toContain('javascript:')
    })

    it('should remove event handlers', () => {
      const input = 'onclick=alert("xss") Hello'
      const result = SecurityUtils.sanitizeInput(input)
      
      expect(result).toBe('alert("xss") Hello')
      expect(result).not.toContain('onclick=')
    })

    it('should handle non-string input', () => {
      const result = SecurityUtils.sanitizeInput(123 as any)
      expect(result).toBe('')
    })
  })

  describe('limitLength', () => {
    it('should limit string length', () => {
      const input = 'This is a very long string that should be truncated'
      const result = SecurityUtils.limitLength(input, 10)
      
      expect(result).toBe('This is a ')
      expect(result.length).toBe(10)
    })

    it('should handle strings shorter than limit', () => {
      const input = 'Short'
      const result = SecurityUtils.limitLength(input, 10)
      
      expect(result).toBe('Short')
    })

    it('should handle non-string input', () => {
      const result = SecurityUtils.limitLength(123 as any, 10)
      expect(result).toBe('')
    })
  })

  describe('checkRateLimit', () => {
    beforeEach(() => {
      // 清理速率限制记录
      SecurityUtils['requestCounts'].clear()
    })

    it('should allow requests within limit', () => {
      const identifier = 'test-user'
      
      for (let i = 0; i < 5; i++) {
        const allowed = SecurityUtils.checkRateLimit(identifier, 10, 60000)
        expect(allowed).toBe(true)
      }
    })

    it('should block requests exceeding limit', () => {
      const identifier = 'test-user'
      
      // 达到限制
      for (let i = 0; i < 10; i++) {
        SecurityUtils.checkRateLimit(identifier, 10, 60000)
      }
      
      // 超出限制
      const blocked = SecurityUtils.checkRateLimit(identifier, 10, 60000)
      expect(blocked).toBe(false)
    })

    it('should reset after time window', async () => {
      const identifier = 'test-user'
      
      // 达到限制
      for (let i = 0; i < 10; i++) {
        SecurityUtils.checkRateLimit(identifier, 10, 100) // 100ms window
      }
      
      // 等待时间窗口过期
      await new Promise(resolve => setTimeout(resolve, 150))
      
      // 应该重新允许请求
      const allowed = SecurityUtils.checkRateLimit(identifier, 10, 100)
      expect(allowed).toBe(true)
    })
  })

  describe('createSafeErrorResponse', () => {
    it('should return detailed error in development', () => {
      const error = new Error('Database connection failed')
      error.stack = 'Error stack trace'
      
      const response = SecurityUtils.createSafeErrorResponse(error, true)
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('Database connection failed')
      expect(response.details).toBe('Error stack trace')
    })

    it('should return safe error in production', () => {
      const error = new Error('Database connection failed')
      
      const response = SecurityUtils.createSafeErrorResponse(error, false)
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('An unexpected error occurred')
      expect(response.details).toBeUndefined()
    })

    it('should map known error types', () => {
      const error = new Error('duplicate key violation')
      
      const response = SecurityUtils.createSafeErrorResponse(error, false)
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('Resource already exists')
    })
  })
})
