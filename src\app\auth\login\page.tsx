'use client'

import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const router = useRouter()
  const { signIn, loading } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!email || !password) {
      setError('Please enter both email and password')
      return
    }

    try {
      console.log('🔍 LoginPage: Attempting sign in...')
      await signIn(email, password)
      console.log('✅ LoginPage: Sign in successful, redirecting to library...')
      // Use window.location.href for a full page redirect to ensure middleware sees the session
      console.log('🔍 LoginPage: Using window.location.href to redirect')
      window.location.href = '/library'
    } catch (err: any) {
      console.error('❌ LoginPage: Login error:', err)
      setError(err.message || 'Login failed. Please check your credentials and try again.')
    }
  }

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Login Form */}
      <section className="pt-32 pb-16">
        <div className="container-max">
          <div className="max-w-md mx-auto">
            <div className="classical-border mb-8 text-center">
              <h1 className="text-4xl md:text-5xl font-display font-semibold mb-4 text-navy-900 text-shadow">
                Welcome Back
              </h1>
              <p className="text-lg text-navy-700 font-body">
                Sign in to access your tutorial library
              </p>
            </div>

            <div className="card">
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg font-body">
                    {error}
                  </div>
                )}

                <div>
                  <label htmlFor="email" className="block text-navy-800 font-display font-medium mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input-field"
                    placeholder="Enter your email"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="password" className="block text-navy-800 font-display font-medium mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input-field"
                    placeholder="Enter your password"
                    required
                  />
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-navy-700 font-body text-sm">Remember me</span>
                  </label>
                  <Link href="/auth/forgot-password" className="text-burgundy-600 hover:text-burgundy-800 font-body text-sm transition-colors">
                    Forgot password?
                  </Link>
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="btn-primary w-full"
                >
                  {loading ? 'Signing In...' : 'Sign In'}
                </button>
              </form>

              <div className="mt-8 pt-6 border-t border-navy-200 text-center">
                <p className="text-navy-600 font-body mb-4">
                  Don't have an account?
                </p>
                <p className="text-navy-600 font-body text-sm">
                  Accounts are automatically created when you purchase your first tutorial.
                </p>
                <Link href="/tutorials" className="btn-outline mt-4 inline-block">
                  Browse Tutorials
                </Link>
              </div>
            </div>

            {/* Demo Instructions */}
            <div className="mt-8 p-4 bg-gold-50 border border-gold-200 rounded-lg">
              <h3 className="font-display font-medium text-navy-800 mb-2">Demo Instructions</h3>
              <p className="text-navy-700 font-body text-sm mb-2">
                This is a demo version. You can log in with any email and password to access the tutorial library.
              </p>
              <p className="text-navy-600 font-body text-xs">
                Example: <EMAIL> / password123
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="max-w-4xl mx-auto text-center">
            <div className="classical-border mb-12">
              <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
                Your Magic Journey Awaits
              </h2>
              <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
                Join thousands of magicians who have elevated their craft with our premium tutorials
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-xl font-display font-medium mb-3 text-burgundy-700">
                  Lifetime Access
                </h3>
                <p className="text-navy-700 font-body">
                  Once purchased, your tutorials are yours forever. Learn at your own pace.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-display font-medium mb-3 text-burgundy-700">
                  Professional Quality
                </h3>
                <p className="text-navy-700 font-body">
                  High-definition video instruction with multiple camera angles and detailed explanations.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-navy-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-display font-medium mb-3 text-burgundy-700">
                  Expert Community
                </h3>
                <p className="text-navy-700 font-body">
                  Connect with fellow magicians and get support from our expert instructors.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
