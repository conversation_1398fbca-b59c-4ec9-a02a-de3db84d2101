const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 Magic Academy Supabase 快速设置\n');

console.log('请按照以下步骤操作：');
console.log('1. 打开 https://supabase.com/dashboard/project/talupaklhwduxpfgwaga/settings/api');
console.log('2. 复制 "service_role" 密钥（不是 anon 密钥）');
console.log('3. 将密钥粘贴到下面\n');

rl.question('请输入您的 Supabase Service Role 密钥: ', (serviceKey) => {
  if (!serviceKey || serviceKey.trim() === '') {
    console.log('❌ 密钥不能为空');
    rl.close();
    return;
  }

  // Update .env.local file
  const envPath = path.join(process.cwd(), '.env.local');
  let envContent = '';
  
  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.log('❌ 无法读取 .env.local 文件');
    rl.close();
    return;
  }

  // Replace or add the service role key
  if (envContent.includes('SUPABASE_SERVICE_ROLE_KEY=')) {
    envContent = envContent.replace(
      /# SUPABASE_SERVICE_ROLE_KEY=".*"/,
      `SUPABASE_SERVICE_ROLE_KEY="${serviceKey.trim()}"`
    );
  } else {
    envContent = envContent.replace(
      '# SUPABASE_SERVICE_ROLE_KEY="your_service_role_key_here"',
      `SUPABASE_SERVICE_ROLE_KEY="${serviceKey.trim()}"`
    );
  }

  try {
    fs.writeFileSync(envPath, envContent);
    console.log('✅ 环境变量已更新');
  } catch (error) {
    console.log('❌ 无法更新 .env.local 文件');
    rl.close();
    return;
  }

  console.log('\n📋 接下来的步骤：');
  console.log('1. 在 Supabase 仪表板中运行数据库架构（SQL Editor）');
  console.log('2. 运行种子数据脚本');
  console.log('\n🔗 有用的链接：');
  console.log('- SQL Editor: https://supabase.com/dashboard/project/talupaklhwduxpfgwaga/sql');
  console.log('- 数据库架构文件: supabase-schema.sql');
  console.log('\n⚡ 快速命令：');
  console.log('npm run check-supabase  # 检查配置');
  console.log('npm run seed-supabase   # 运行种子数据');

  rl.close();
});

rl.on('close', () => {
  console.log('\n👋 设置完成！');
  process.exit(0);
});
