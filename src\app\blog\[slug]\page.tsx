import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { BlogPost } from '@/types'
import { notFound } from 'next/navigation'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

// 临时示例数据，稍后会从数据文件中读取
const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'The Philosophy of Wonder: Understanding the True Nature of Magic',
    content: `# The Philosophy of Wonder: Understanding the True Nature of Magic

Magic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.

## The Essence of Wonder

Wonder is a fundamental human emotion that connects us to the mystery of existence. When we experience true wonder, we are momentarily transported beyond the mundane concerns of daily life into a realm where anything seems possible.

> "The most beautiful thing we can experience is the mysterious. It is the source of all true art and science." - <PERSON>

This quote, though not specifically about magic, captures the essence of what we strive to create as magical artists. We are not merely entertainers; we are custodians of mystery, guardians of wonder.

## The Difference Between Tricks and Magic

There is a profound difference between performing tricks and creating magic:

- **Tricks** are mechanical demonstrations of skill or cleverness
- **Magic** is the creation of impossible moments that touch the soul
- **Tricks** impress the mind
- **Magic** moves the heart

### The Role of the Magician

The true magician serves as a bridge between the possible and the impossible, the known and the unknown. We do not simply fool people; we invite them to experience a different way of seeing the world.

## Creating Authentic Magical Moments

To create authentic magical moments, we must:

1. **Understand our audience** - Connect with their hopes, fears, and dreams
2. **Master our craft** - Technical skill is the foundation upon which artistry is built
3. **Embrace vulnerability** - True magic happens when we allow ourselves to be genuinely present
4. **Serve the mystery** - We are not the star; the impossible moment is

## The Responsibility of Wonder

With the power to create wonder comes great responsibility. We must:

- Never use our abilities to harm or deceive for personal gain
- Respect the intelligence and dignity of our audiences
- Preserve the sense of mystery that makes magic possible
- Pass on our knowledge to worthy successors

## Conclusion

Magic, at its highest form, is a spiritual practice that connects us to the fundamental mystery of existence. When we approach our art with reverence, skill, and genuine care for our audiences, we become part of an ancient tradition that has always sought to remind humanity that there is more to this world than meets the eye.

The next time you perform, ask yourself: Am I creating a trick, or am I creating magic? The answer will determine not just the quality of your performance, but the depth of its impact on those who witness it.`,
    excerpt: 'Magic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.',
    slug: 'philosophy-of-wonder',
    publishedAt: new Date('2024-06-15'),
    createdAt: new Date('2024-06-15'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: '2',
    title: 'The Lost Art of Misdirection: Classical Techniques for Modern Performers',
    content: `# The Lost Art of Misdirection: Classical Techniques for Modern Performers

Misdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.

## Understanding True Misdirection

Misdirection is not about forcing attention away from something, but about naturally guiding it toward what you want the audience to see. The best misdirection feels completely natural and unforced.

### The Three Pillars of Classical Misdirection

1. **Physical Misdirection** - Using body language and movement
2. **Psychological Misdirection** - Leveraging human nature and expectations  
3. **Temporal Misdirection** - Controlling the timing of attention

## Physical Misdirection Techniques

The masters understood that the human eye naturally follows movement, particularly the movement of the performer's eyes and hands.

### The Power of Eye Contact

Your eyes are your most powerful tool for misdirection:
- Where you look, the audience looks
- Breaking eye contact creates a moment of vulnerability that draws attention
- Sustained eye contact creates intimacy and trust

### Hand Positioning and Movement

Classical hand positioning follows these principles:
- Natural gestures feel invisible
- Unnatural gestures draw attention
- The hand that moves last is remembered most

## Psychological Misdirection

This is perhaps the most sophisticated form of misdirection, relying on understanding human psychology rather than physical manipulation.

### Expectation Management

People see what they expect to see. By carefully managing expectations, you can:
- Make the impossible seem natural
- Hide methods in plain sight
- Create surprise through violated expectations

### The Principle of Divided Attention

The human mind can only focus on one thing at a time. By giving the audience something interesting to think about, you can perform your method while their minds are occupied.

## Temporal Misdirection

Timing is everything in misdirection. The classical approach emphasizes:

- **The moment of maximum attention** - When all eyes are on you
- **The moment of relaxed attention** - When the audience thinks nothing important is happening
- **The moment of surprise** - When the impossible is revealed

## Modern Applications of Classical Principles

These timeless principles can be applied to contemporary performance:

### In Close-Up Magic
- Use conversation to create psychological misdirection
- Employ natural gestures for physical misdirection
- Time your methods during moments of laughter or surprise

### In Stage Magic
- Use lighting and staging for physical misdirection
- Employ narrative and character for psychological misdirection
- Control pacing for temporal misdirection

## Common Mistakes in Modern Misdirection

Many contemporary performers make these errors:

1. **Over-directing** - Being too obvious about where they want attention
2. **Under-motivating** - Not giving the audience a reason to look where they want
3. **Poor timing** - Rushing the misdirection or holding it too long

## Conclusion

The art of misdirection is not about deception, but about creating a shared experience of wonder. When done correctly, the audience never feels fooled or manipulated, but rather feels they have participated in something magical.

Study the masters, practice the principles, and remember: the best misdirection is the misdirection that no one notices.`,
    excerpt: 'Misdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.',
    slug: 'lost-art-misdirection',
    publishedAt: new Date('2024-05-28'),
    createdAt: new Date('2024-05-28'),
    updatedAt: new Date('2024-05-28')
  }
]

interface BlogDetailPageProps {
  params: {
    slug: string
  }
}

export default function BlogDetailPage({ params }: BlogDetailPageProps) {
  const post = blogPosts.find(post => post.slug === params.slug)
  
  if (!post) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Breadcrumb */}
      <section className="pt-32 pb-8">
        <div className="container-max">
          <nav className="flex items-center space-x-2 text-sm font-body">
            <Link href="/" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Home
            </Link>
            <span className="text-navy-400">→</span>
            <Link href="/blog" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Blog
            </Link>
            <span className="text-navy-400">→</span>
            <span className="text-burgundy-700 font-medium">{post.title}</span>
          </nav>
        </div>
      </section>

      {/* Article Header */}
      <section className="pb-12">
        <div className="container-max">
          <div className="max-w-4xl mx-auto text-center">
            <div className="classical-border mb-8">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-semibold mb-6 text-navy-900 text-shadow leading-tight">
                {post.title}
              </h1>
            </div>
            
            <div className="flex items-center justify-center space-x-6 text-navy-600 font-body mb-8">
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {post.publishedAt?.toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </span>
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                8 min read
              </span>
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                Philosophy
              </span>
            </div>
            
            <p className="text-xl text-navy-700 font-body leading-relaxed max-w-3xl mx-auto">
              {post.excerpt}
            </p>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="max-w-4xl mx-auto">
            <article className="prose prose-lg prose-navy max-w-none">
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({children}) => (
                    <h1 className="text-4xl md:text-5xl font-display font-semibold mb-8 text-navy-900 text-shadow border-b-2 border-gold-200 pb-4">
                      {children}
                    </h1>
                  ),
                  h2: ({children}) => (
                    <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 mt-12 text-burgundy-700 relative">
                      <span className="text-gold-600 mr-3">❦</span>
                      {children}
                    </h2>
                  ),
                  h3: ({children}) => (
                    <h3 className="text-2xl md:text-3xl font-display font-medium mb-4 mt-8 text-navy-800">
                      {children}
                    </h3>
                  ),
                  p: ({children}) => (
                    <p className="text-lg text-navy-800 font-body leading-relaxed mb-6">
                      {children}
                    </p>
                  ),
                  blockquote: ({children}) => (
                    <blockquote className="border-l-4 border-gold-500 pl-6 py-4 my-8 bg-cream-200 rounded-r-lg italic text-lg text-navy-700 font-display">
                      {children}
                    </blockquote>
                  ),
                  ul: ({children}) => (
                    <ul className="list-none space-y-3 mb-6 text-lg text-navy-800 font-body">
                      {children}
                    </ul>
                  ),
                  ol: ({children}) => (
                    <ol className="list-decimal list-inside space-y-3 mb-6 text-lg text-navy-800 font-body pl-4">
                      {children}
                    </ol>
                  ),
                  li: ({children}) => (
                    <li className="flex items-start">
                      <span className="text-burgundy-600 mr-3 mt-1">•</span>
                      <span>{children}</span>
                    </li>
                  ),
                  strong: ({children}) => (
                    <strong className="font-semibold text-burgundy-700">
                      {children}
                    </strong>
                  ),
                  em: ({children}) => (
                    <em className="italic text-navy-700 font-display">
                      {children}
                    </em>
                  ),
                  code: ({children}) => (
                    <code className="bg-navy-100 text-burgundy-700 px-2 py-1 rounded font-mono text-base">
                      {children}
                    </code>
                  )
                }}
              >
                {post.content}
              </ReactMarkdown>
            </article>
          </div>
        </div>
      </section>

      {/* Article Footer */}
      <section className="section-padding">
        <div className="container-max">
          <div className="max-w-4xl mx-auto">
            <div className="ornamental-divider mb-8"></div>
            
            {/* Share and Navigation */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-6">
              <div className="flex items-center space-x-4">
                <span className="text-navy-600 font-display">Share this article:</span>
                <div className="flex space-x-3">
                  <button className="w-10 h-10 bg-navy-700 hover:bg-navy-800 text-cream-50 rounded-full flex items-center justify-center transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                    </svg>
                  </button>
                  <button className="w-10 h-10 bg-navy-700 hover:bg-navy-800 text-cream-50 rounded-full flex items-center justify-center transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                    </svg>
                  </button>
                  <button className="w-10 h-10 bg-navy-700 hover:bg-navy-800 text-cream-50 rounded-full flex items-center justify-center transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </button>
                </div>
              </div>
              
              <Link href="/blog" className="btn-outline">
                ← Back to All Articles
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="classical-border mb-12 text-center">
            <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
              Related Articles
            </h2>
            <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Continue exploring our collection of magical insights
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {blogPosts
              .filter(relatedPost => relatedPost.id !== post.id)
              .slice(0, 2)
              .map((relatedPost) => (
                <Link 
                  key={relatedPost.id} 
                  href={`/blog/${relatedPost.slug}`}
                  className="group block"
                >
                  <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300">
                    <h3 className="text-xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {relatedPost.title}
                    </h3>
                    <p className="text-navy-700 font-body leading-relaxed mb-4">
                      {relatedPost.excerpt.substring(0, 150)}...
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-navy-500 font-body">
                        {relatedPost.publishedAt?.toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </span>
                      <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                        Read Article →
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
