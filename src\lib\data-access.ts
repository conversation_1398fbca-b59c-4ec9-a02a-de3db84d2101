import { supabase } from './supabase'
import { BlogPost, Tutorial, PortfolioItem, Video } from '@/types'

// Blog Posts Data Access
export class BlogDataAccess {
  static async getAll(): Promise<BlogPost[]> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching blog posts:', error)
      throw error
    }

    return data.map(post => ({
      id: post.id,
      title: post.title,
      content: post.content,
      excerpt: post.excerpt,
      slug: post.slug,
      featured: post.featured,
      published: post.published,
      author: post.author,
      tags: post.tags || [],
      publishedAt: new Date(post.created_at),
      createdAt: new Date(post.created_at),
      updatedAt: new Date(post.updated_at)
    }))
  }

  static async getBySlug(slug: string): Promise<BlogPost | null> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching blog post:', error)
      throw error
    }

    return {
      id: data.id,
      title: data.title,
      content: data.content,
      excerpt: data.excerpt,
      slug: data.slug,
      featured: data.featured,
      published: data.published,
      author: data.author,
      tags: data.tags || [],
      publishedAt: new Date(data.created_at),
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async getFeatured(limit?: number): Promise<BlogPost[]> {
    let query = supabase
      .from('blog_posts')
      .select('*')
      .eq('published', true)
      .eq('featured', true)
      .order('created_at', { ascending: false })

    if (limit) {
      query = query.limit(limit)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching featured blog posts:', error)
      throw error
    }

    return data.map(post => ({
      id: post.id,
      title: post.title,
      content: post.content,
      excerpt: post.excerpt,
      slug: post.slug,
      featured: post.featured,
      published: post.published,
      author: post.author,
      tags: post.tags || [],
      publishedAt: new Date(post.created_at),
      createdAt: new Date(post.created_at),
      updatedAt: new Date(post.updated_at)
    }))
  }
}

// Tutorials Data Access
export class TutorialDataAccess {
  static async getAll(): Promise<Tutorial[]> {
    const { data, error } = await supabase
      .from('tutorials')
      .select(`
        *,
        videos (
          id,
          title,
          description,
          duration,
          order_index,
          video_url
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tutorials:', error)
      throw error
    }

    return data.map(tutorial => ({
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      price: tutorial.price,
      coverImage: tutorial.cover_image,
      featured: tutorial.featured,
      difficulty: tutorial.difficulty,
      duration: tutorial.duration,
      previewVideoUrl: tutorial.preview_video_url,
      videos: tutorial.videos?.map((video: any) => ({
        id: video.id,
        title: video.title,
        description: video.description,
        duration: video.duration,
        videoUrl: video.video_url,
        thumbnailUrl: '', // Add if needed
        order: video.order_index,
        tutorialId: tutorial.id
      })) || [],
      createdAt: new Date(tutorial.created_at),
      updatedAt: new Date(tutorial.updated_at)
    }))
  }

  static async getById(id: string): Promise<Tutorial | null> {
    const { data, error } = await supabase
      .from('tutorials')
      .select(`
        *,
        videos (
          id,
          title,
          description,
          duration,
          order_index,
          video_url
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching tutorial:', error)
      throw error
    }

    return {
      id: data.id,
      title: data.title,
      description: data.description,
      price: data.price,
      coverImage: data.cover_image,
      featured: data.featured,
      difficulty: data.difficulty,
      duration: data.duration,
      previewVideoUrl: data.preview_video_url,
      videos: data.videos?.map((video: any) => ({
        id: video.id,
        title: video.title,
        description: video.description,
        duration: video.duration,
        videoUrl: video.video_url,
        thumbnailUrl: '', // Add if needed
        order: video.order_index,
        tutorialId: data.id
      })) || [],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }
  }

  static async getFeatured(): Promise<Tutorial[]> {
    const { data, error } = await supabase
      .from('tutorials')
      .select(`
        *,
        videos (
          id,
          title,
          description,
          duration,
          order_index,
          video_url
        )
      `)
      .eq('featured', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching featured tutorials:', error)
      throw error
    }

    return data.map(tutorial => ({
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      price: tutorial.price,
      coverImage: tutorial.cover_image,
      featured: tutorial.featured,
      difficulty: tutorial.difficulty,
      duration: tutorial.duration,
      previewVideoUrl: tutorial.preview_video_url,
      videos: tutorial.videos?.map((video: any) => ({
        id: video.id,
        title: video.title,
        description: video.description,
        duration: video.duration,
        videoUrl: video.video_url,
        thumbnailUrl: '', // Add if needed
        order: video.order_index,
        tutorialId: tutorial.id
      })) || [],
      createdAt: new Date(tutorial.created_at),
      updatedAt: new Date(tutorial.updated_at)
    }))
  }
}

// Portfolio Data Access
export class PortfolioDataAccess {
  static async getAll(): Promise<PortfolioItem[]> {
    const { data, error } = await supabase
      .from('portfolio')
      .select('*')
      .order('date', { ascending: false })

    if (error) {
      console.error('Error fetching portfolio items:', error)
      throw error
    }

    return data.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description,
      mediaUrl: item.image_url,
      mediaType: item.image_url?.includes('.mp4') ? 'video' as const : 'image' as const,
      category: item.category,
      featured: item.featured,
      order: 1, // Add order field if needed in schema
      createdAt: new Date(item.created_at)
    }))
  }

  static async getById(id: string): Promise<PortfolioItem | null> {
    const { data, error } = await supabase
      .from('portfolio')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching portfolio item:', error)
      throw error
    }

    return {
      id: data.id,
      title: data.title,
      description: data.description,
      mediaUrl: data.image_url,
      mediaType: data.image_url?.includes('.mp4') ? 'video' as const : 'image' as const,
      category: data.category,
      featured: data.featured,
      order: 1, // Add order field if needed in schema
      createdAt: new Date(data.created_at)
    }
  }

  static async getFeatured(): Promise<PortfolioItem[]> {
    const { data, error } = await supabase
      .from('portfolio')
      .select('*')
      .eq('featured', true)
      .order('date', { ascending: false })

    if (error) {
      console.error('Error fetching featured portfolio items:', error)
      throw error
    }

    return data.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description,
      mediaUrl: item.image_url,
      mediaType: item.image_url?.includes('.mp4') ? 'video' as const : 'image' as const,
      category: item.category,
      featured: item.featured,
      order: 1, // Add order field if needed in schema
      createdAt: new Date(data.created_at)
    }))
  }
}
