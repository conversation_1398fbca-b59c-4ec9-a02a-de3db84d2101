'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { SupabaseAuthService } from '@/lib/auth-supabase'
import type { AppUser, Tutorial } from '@/types'
import type { Session } from '@supabase/supabase-js'

interface AuthContextType {
  user: AppUser | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, name?: string) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AppUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // Initialize auth state
  useEffect(() => {
    console.log('🔍 AuthContext: Initializing auth state...')
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('🔍 AuthContext: Initial session:', session)
      setSession(session)
      if (session?.user) {
        console.log('✅ AuthContext: User found in session, loading profile...')
        loadUserProfile(session.user.id)
      } else {
        console.log('❌ AuthContext: No user in session')
        setLoading(false)
      }
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔍 AuthContext: Auth state change:', event, session)
      setSession(session)

      if (session?.user) {
        console.log('✅ AuthContext: User authenticated via state change, loading profile...')
        await loadUserProfile(session.user.id)
      } else {
        console.log('❌ AuthContext: User signed out via state change')
        setUser(null)
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserProfile = async (userId: string) => {
    try {
      console.log('🔍 AuthContext: Loading user profile for ID:', userId)
      const userProfile = await SupabaseAuthService.getCurrentUser()
      console.log('✅ AuthContext: User profile loaded:', userProfile)
      setUser(userProfile)
    } catch (error) {
      console.error('❌ AuthContext: Error loading user profile:', error)
      setUser(null)
    } finally {
      console.log('🔍 AuthContext: Profile loading complete')
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    console.log('🔍 AuthContext: Sign in attempt for:', email)
    setLoading(true)
    try {
      const { user: authUser } = await SupabaseAuthService.signIn(email, password)
      console.log('✅ AuthContext: Sign in successful:', authUser)

      // Wait for the user profile to be loaded
      await loadUserProfile(authUser.id)
      console.log('✅ AuthContext: User profile loaded after sign in')
    } catch (error) {
      console.error('❌ AuthContext: Sign in failed:', error)
      setLoading(false)
      throw error
    }
  }

  const signUp = async (email: string, password: string, name?: string) => {
    setLoading(true)
    try {
      await SupabaseAuthService.signUp(email, password, name)
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await SupabaseAuthService.signOut()
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const refreshUser = async () => {
    if (session?.user) {
      await loadUserProfile(session.user.id)
    }
  }

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    refreshUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth()

    console.log('🔍 withAuth: Loading:', loading, 'User:', user)

    if (loading) {
      console.log('🔍 withAuth: Showing loading screen')
      return (
        <div className="min-h-screen bg-cream-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-600 mx-auto mb-4"></div>
            <p className="text-navy-700 font-body">Loading...</p>
          </div>
        </div>
      )
    }

    if (!user) {
      console.log('❌ withAuth: No user, redirecting to login')
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      return null
    }

    console.log('✅ withAuth: User authenticated, rendering component')
    return <Component {...props} />
  }
}

// Hook for checking if user has purchased a tutorial
export function useTutorialAccess(tutorialId: string) {
  const { user } = useAuth()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function checkAccess() {
      console.log('🔍 useTutorialAccess: Checking access for tutorial:', tutorialId, 'user:', user?.id)

      if (!user || !tutorialId) {
        console.log('❌ useTutorialAccess: No user or tutorial ID')
        setHasAccess(false)
        setLoading(false)
        return
      }

      try {
        const { PurchaseModel } = await import('@/lib/database')
        const purchased = await PurchaseModel.hasUserPurchased(user.id, tutorialId)
        console.log('✅ useTutorialAccess: Access check result:', purchased)
        setHasAccess(purchased)
      } catch (error) {
        console.error('❌ useTutorialAccess: Error checking tutorial access:', error)
        setHasAccess(false)
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [user, tutorialId])

  return { hasAccess, loading }
}

// Hook for getting user's purchased tutorials
export function usePurchasedTutorials() {
  const { user } = useAuth()
  const [purchasedTutorials, setPurchasedTutorials] = useState<Tutorial[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchPurchasedTutorials() {
      console.log('🔍 usePurchasedTutorials: Fetching tutorials for user:', user)

      if (!user) {
        console.log('❌ usePurchasedTutorials: No user, setting empty tutorials')
        setPurchasedTutorials([])
        setLoading(false)
        return
      }

      try {
        console.log('🔍 usePurchasedTutorials: Loading database models...')
        const { PurchaseModel, tutorialModel } = await import('@/lib/database')

        console.log('🔍 usePurchasedTutorials: Getting user purchases...')
        const purchases = await PurchaseModel.getUserPurchases(user.id)
        console.log('✅ usePurchasedTutorials: Found purchases:', purchases)

        // Get tutorial details for each purchase
        console.log('🔍 usePurchasedTutorials: Getting tutorial details...')
        const tutorials = await Promise.all(
          purchases.map(async (purchase) => {
            const tutorial = await tutorialModel.getById(purchase.tutorialId)
            console.log('🔍 usePurchasedTutorials: Tutorial for purchase', purchase.tutorialId, ':', tutorial)
            return tutorial
          })
        )

        // Filter out any null tutorials
        const validTutorials = tutorials.filter(tutorial => tutorial !== null) as Tutorial[]
        console.log('✅ usePurchasedTutorials: Valid tutorials:', validTutorials)
        setPurchasedTutorials(validTutorials)
      } catch (error) {
        console.error('❌ usePurchasedTutorials: Error fetching purchased tutorials:', error)
        setPurchasedTutorials([])
      } finally {
        console.log('🔍 usePurchasedTutorials: Loading complete')
        setLoading(false)
      }
    }

    fetchPurchasedTutorials()
  }, [user])

  return { purchasedTutorials, loading }
}
