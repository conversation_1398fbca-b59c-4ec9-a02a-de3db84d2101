import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAuthService } from '@/lib/auth-supabase'

export async function POST(request: NextRequest) {
  try {
    const { email, password, name } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const { user, session } = await SupabaseAuthService.signUp(email, password, name)

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user?.id,
          email: user?.email,
          name: name || email.split('@')[0]
        },
        session: session ? {
          access_token: session.access_token,
          refresh_token: session.refresh_token
        } : null,
        message: 'Registration successful. Please check your email to confirm your account.'
      }
    })
  } catch (error: any) {
    console.error('Registration API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Registration failed' 
      },
      { status: 400 }
    )
  }
}
