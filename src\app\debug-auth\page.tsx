'use client'

import { useAuth, useTutorialAccess } from '@/contexts/AuthContext'
import Navigation from '@/components/Navigation'

export default function DebugAuthPage() {
  const { user, session, loading } = useAuth()
  const { hasAccess, loading: accessLoading } = useTutorialAccess('1')

  return (
    <div className="min-h-screen bg-cream-50">
      <Navigation />
      <div className="pt-32 container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Authentication Debug</h1>
        
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Auth State</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>
              <p><strong>Session:</strong> {session ? 'Active' : 'None'}</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Tutorial Access (ID: 1)</h2>
            <div className="space-y-2">
              <p><strong>Access Loading:</strong> {accessLoading ? 'Yes' : 'No'}</p>
              <p><strong>Has Access:</strong> {hasAccess === null ? 'null' : hasAccess ? 'Yes' : 'No'}</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Local Storage</h2>
            <div className="space-y-2">
              <p><strong>User in localStorage:</strong> {typeof window !== 'undefined' ? localStorage.getItem('user') || 'null' : 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
