import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

console.log('🔍 Checking Supabase configuration...\n')

// Check environment variables
console.log('📋 Environment Variables:')
console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing')
console.log('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing')

if (!supabaseUrl || !supabaseServiceKey) {
  console.log('\n❌ Missing required environment variables. Please check your .env.local file.')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function checkDatabase() {
  try {
    console.log('\n🗄️ Checking database tables...')
    
    // Check if tables exist by trying to query them
    const tables = ['profiles', 'tutorials', 'videos', 'purchases', 'blog_posts', 'portfolio']
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1)
        
        if (error) {
          console.log(`❌ Table '${table}': ${error.message}`)
        } else {
          console.log(`✅ Table '${table}': OK`)
        }
      } catch (err) {
        console.log(`❌ Table '${table}': Error checking`)
      }
    }
    
    console.log('\n👤 Checking for test user...')
    const { data: users, error: userError } = await supabase.auth.admin.listUsers()
    
    if (userError) {
      console.log('❌ Error checking users:', userError.message)
    } else {
      const testUser = users.users.find(user => user.email === '<EMAIL>')
      if (testUser) {
        console.log('✅ Test user (<EMAIL>) exists')
      } else {
        console.log('❌ Test user (<EMAIL>) not found')
      }
    }
    
    console.log('\n📊 Checking data counts...')
    const dataCounts = await Promise.all([
      supabase.from('tutorials').select('*', { count: 'exact', head: true }),
      supabase.from('videos').select('*', { count: 'exact', head: true }),
      supabase.from('blog_posts').select('*', { count: 'exact', head: true }),
      supabase.from('portfolio').select('*', { count: 'exact', head: true }),
      supabase.from('purchases').select('*', { count: 'exact', head: true })
    ])
    
    const [tutorials, videos, blogPosts, portfolio, purchases] = dataCounts
    
    console.log(`📚 Tutorials: ${tutorials.count || 0}`)
    console.log(`🎥 Videos: ${videos.count || 0}`)
    console.log(`📝 Blog Posts: ${blogPosts.count || 0}`)
    console.log(`🎨 Portfolio Items: ${portfolio.count || 0}`)
    console.log(`🛒 Purchases: ${purchases.count || 0}`)
    
    console.log('\n🎉 Database check completed!')
    
  } catch (error) {
    console.error('❌ Error checking database:', error)
  }
}

checkDatabase()
