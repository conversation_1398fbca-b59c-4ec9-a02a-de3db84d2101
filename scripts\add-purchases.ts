import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function addPurchases() {
  try {
    console.log('🛒 Adding purchase records for test user...\n')

    // Get the test user
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError)
      return
    }

    const testUser = users.users.find(user => user.email === '<EMAIL>')
    if (!testUser) {
      console.error('❌ Test user not found')
      return
    }

    console.log('✅ Found test user:', testUser.email)

    // Get tutorials
    const { data: tutorials, error: tutorialsError } = await supabase
      .from('tutorials')
      .select('*')
    
    if (tutorialsError) {
      console.error('❌ Error fetching tutorials:', tutorialsError)
      return
    }

    console.log('✅ Found tutorials:', tutorials?.length)

    // Create purchase records for the first two tutorials
    const purchasesToCreate = [
      {
        id: 'purchase-1',
        user_id: testUser.id,
        tutorial_id: '1', // The Art of Card Control
        amount: 49.99,
        paypal_order_id: 'DEMO-ORDER-123',
        status: 'completed'
      },
      {
        id: 'purchase-2',
        user_id: testUser.id,
        tutorial_id: '2', // Advanced False Shuffles
        amount: 79.99,
        paypal_order_id: 'DEMO-ORDER-456',
        status: 'completed'
      }
    ]

    console.log('📝 Creating purchase records...')
    
    for (const purchase of purchasesToCreate) {
      const { error: purchaseError } = await supabase
        .from('purchases')
        .insert([purchase])
      
      if (purchaseError) {
        console.error(`❌ Error creating purchase ${purchase.id}:`, purchaseError)
      } else {
        console.log(`✅ Created purchase ${purchase.id} for tutorial ${purchase.tutorial_id}`)
      }
    }

    // Verify purchases were created
    console.log('\n🔍 Verifying purchases...')
    const { data: purchases, error: verifyError } = await supabase
      .from('purchases')
      .select('*')
      .eq('user_id', testUser.id)
    
    if (verifyError) {
      console.error('❌ Error verifying purchases:', verifyError)
    } else {
      console.log(`✅ User now has ${purchases?.length || 0} purchases`)
      purchases?.forEach(purchase => {
        console.log(`  - Tutorial ${purchase.tutorial_id}: ${purchase.status}`)
      })
    }

    console.log('\n🎉 Purchase records added successfully!')
    console.log('\n📋 Test User Credentials:')
    console.log('Email: <EMAIL>')
    console.log('Password: password123')
    console.log('\n🚀 The user should now have access to purchased tutorials in their library!')

  } catch (error) {
    console.error('❌ Error adding purchases:', error)
  }
}

addPurchases()
