import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug API: Starting debug check...')
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Test database connection
    const { data: tutorials, error: tutorialsError } = await supabase
      .from('tutorials')
      .select('*')
      .limit(3)

    const { data: purchases, error: purchasesError } = await supabase
      .from('purchases')
      .select('*')
      .limit(5)

    // Test auth
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()

    const debugInfo = {
      timestamp: new Date().toISOString(),
      supabaseUrl,
      database: {
        tutorials: {
          count: tutorials?.length || 0,
          error: tutorialsError?.message || null,
          data: tutorials?.map(t => ({ id: t.id, title: t.title })) || []
        },
        purchases: {
          count: purchases?.length || 0,
          error: purchasesError?.message || null,
          data: purchases?.map(p => ({ 
            id: p.id, 
            user_id: p.user_id, 
            tutorial_id: p.tutorial_id,
            status: p.status 
          })) || []
        }
      },
      auth: {
        userCount: users?.users?.length || 0,
        error: usersError?.message || null,
        testUser: users?.users?.find(u => u.email === '<EMAIL>') ? 'Found' : 'Not found'
      }
    }

    console.log('✅ Debug API: Debug info:', debugInfo)
    
    return NextResponse.json(debugInfo)
  } catch (error) {
    console.error('❌ Debug API: Error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
