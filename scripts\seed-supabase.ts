import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY! // Service role key for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:')
  console.error('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✓' : '✗')
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✓' : '✗')
  console.error('\nPlease add SUPABASE_SERVICE_ROLE_KEY to your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Sample data - 整合所有页面中的硬编码数据
const sampleTutorials = [
  {
    id: '1',
    title: 'The Classical Card Force',
    description: 'Master the most elegant and deceptive card force techniques used by professional magicians for over a century.',
    price: 49.99,
    featured: true,
    difficulty: 'intermediate',
    duration: 3600, // 1 hour in seconds
    cover_image: '/images/tutorials/card-force.jpg',
    preview_video_url: '/videos/previews/card-force-preview.mp4'
  },
  {
    id: '2',
    title: 'Advanced Sleight of Hand',
    description: 'Develop lightning-fast finger dexterity and learn the secret moves that separate amateurs from professionals.',
    price: 79.99,
    featured: true,
    difficulty: 'advanced',
    duration: 4800, // 80 minutes
    cover_image: '/images/tutorials/sleight-of-hand.jpg',
    preview_video_url: '/videos/previews/sleight-of-hand-preview.mp4'
  },
  {
    id: '3',
    title: 'Mentalism Fundamentals',
    description: 'Unlock the mysteries of the mind with psychological techniques and methods for creating impossible predictions.',
    price: 59.99,
    featured: true,
    difficulty: 'intermediate',
    duration: 3600, // 60 minutes
    cover_image: '/images/tutorials/mentalism.jpg',
    preview_video_url: '/videos/previews/mentalism-preview.mp4'
  },
  {
    id: '4',
    title: 'The Art of Misdirection',
    description: 'Learn the subtle psychological principles that allow magicians to control attention and create impossible moments.',
    price: 39.99,
    featured: false,
    difficulty: 'intermediate',
    duration: 2400, // 40 minutes
    cover_image: '/images/tutorials/misdirection.jpg',
    preview_video_url: '/videos/previews/misdirection-preview.mp4'
  },
  {
    id: '5',
    title: 'Professional Stage Presence',
    description: 'Transform your performance with commanding stage presence, theatrical timing, and audience connection techniques.',
    price: 69.99,
    featured: false,
    difficulty: 'intermediate',
    duration: 4200, // 70 minutes
    cover_image: '/images/tutorials/stage-presence.jpg',
    preview_video_url: '/videos/previews/stage-presence-preview.mp4'
  },
  {
    id: '6',
    title: 'Close-Up Magic Mastery',
    description: 'Perfect your intimate magic skills with advanced techniques for table-side and parlor performances.',
    price: 54.99,
    featured: false,
    difficulty: 'advanced',
    duration: 3900, // 65 minutes
    cover_image: '/images/tutorials/close-up.jpg',
    preview_video_url: '/videos/previews/close-up-preview.mp4'
  },
  {
    id: '7',
    title: 'Advanced Card Control Techniques',
    description: 'Master the art of controlling cards with precision and elegance. This comprehensive tutorial covers advanced methods for maintaining control of selected cards throughout complex routines.',
    price: 29.99,
    featured: false,
    difficulty: 'advanced',
    duration: 2700, // 45 minutes
    cover_image: '/images/tutorials/card-control.jpg',
    preview_video_url: '/videos/previews/card-control-advanced-preview.mp4'
  },
  {
    id: '8',
    title: 'Invisible Deck Mastery',
    description: 'Learn the secrets behind one of magic\'s most powerful tools. From basic handling to advanced presentations, this tutorial will transform your invisible deck work.',
    price: 39.99,
    featured: false,
    difficulty: 'intermediate',
    duration: 3000, // 50 minutes
    cover_image: '/images/tutorials/invisible-deck.jpg',
    preview_video_url: '/videos/previews/invisible-deck-preview.mp4'
  },
  {
    id: '9',
    title: 'Psychological Forces and Mentalism',
    description: 'Dive deep into the psychology of magic and learn how to influence spectator choices without them knowing. Essential techniques for any serious mentalist.',
    price: 49.99,
    featured: false,
    difficulty: 'advanced',
    duration: 3300, // 55 minutes
    cover_image: '/images/tutorials/mentalism.jpg',
    preview_video_url: '/videos/previews/psychological-forces-preview.mp4'
  }
]

const sampleVideos = [
  // Videos for Tutorial 1 - The Classical Card Force
  {
    id: '1-1',
    tutorial_id: '1',
    title: 'Introduction to Card Forces',
    description: 'Understanding the psychology behind forcing cards',
    duration: 480, // 8 minutes
    order_index: 1,
    video_url: '/videos/tutorials/card-force-intro.mp4'
  },
  {
    id: '1-2',
    tutorial_id: '1',
    title: 'The Hindu Force',
    description: 'Master the classic Hindu force technique',
    duration: 720, // 12 minutes
    order_index: 2,
    video_url: '/videos/tutorials/hindu-force.mp4'
  },
  {
    id: '1-3',
    tutorial_id: '1',
    title: 'Advanced Force Variations',
    description: 'Multiple variations of classical card forces',
    duration: 900, // 15 minutes
    order_index: 3,
    video_url: '/videos/tutorials/1/advanced-forces.mp4'
  },
  // Videos for Tutorial 2 - Advanced Sleight of Hand
  {
    id: '2-1',
    tutorial_id: '2',
    title: 'Finger Dexterity Exercises',
    description: 'Essential exercises for developing finger strength and dexterity',
    duration: 1200, // 20 minutes
    order_index: 1,
    video_url: '/videos/tutorials/2/finger-exercises.mp4'
  },
  {
    id: '2-2',
    tutorial_id: '2',
    title: 'The Classic Palm',
    description: 'Master the fundamental palming technique',
    duration: 1500, // 25 minutes
    order_index: 2,
    video_url: '/videos/tutorials/2/classic-palm.mp4'
  },
  {
    id: '2-3',
    tutorial_id: '2',
    title: 'Advanced Palming Techniques',
    description: 'Multiple palm variations and applications',
    duration: 1800, // 30 minutes
    order_index: 3,
    video_url: '/videos/tutorials/2/advanced-palming.mp4'
  },
  // Videos for Tutorial 3 - Mentalism Fundamentals
  {
    id: '3-1',
    tutorial_id: '3',
    title: 'Psychology of Mentalism',
    description: 'Understanding the psychological principles behind mentalism',
    duration: 900, // 15 minutes
    order_index: 1,
    video_url: '/videos/tutorials/3/psychology-basics.mp4'
  },
  {
    id: '3-2',
    tutorial_id: '3',
    title: 'Cold Reading Techniques',
    description: 'Learn the art of cold reading for mentalism effects',
    duration: 1200, // 20 minutes
    order_index: 2,
    video_url: '/videos/tutorials/3/cold-reading.mp4'
  },
  {
    id: '3-3',
    tutorial_id: '3',
    title: 'Prediction Methods',
    description: 'Various methods for creating impossible predictions',
    duration: 1500, // 25 minutes
    order_index: 3,
    video_url: '/videos/tutorials/3/predictions.mp4'
  }
]

const sampleBlogPosts = [
  {
    title: 'The Philosophy of Wonder: Understanding the True Nature of Magic',
    slug: 'philosophy-of-wonder',
    content: `# The Philosophy of Wonder: Understanding the True Nature of Magic

Magic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.

## The Essence of Wonder

Wonder is a fundamental human emotion that connects us to the mystery of existence. When we experience true wonder, we are momentarily transported beyond the mundane concerns of daily life into a realm where anything seems possible.

> "The most beautiful thing we can experience is the mysterious. It is the source of all true art and science." - Albert Einstein

This quote, though not specifically about magic, captures the essence of what we strive to create as magical artists. We are not merely entertainers; we are custodians of mystery, guardians of wonder.

## The Difference Between Tricks and Magic

There is a profound difference between performing tricks and creating magic:

- **Tricks** are mechanical demonstrations of skill or cleverness
- **Magic** is the creation of impossible moments that touch the soul
- **Tricks** impress the mind
- **Magic** moves the heart

### The Role of the Magician

The true magician serves as a bridge between the possible and the impossible, the known and the unknown. We do not simply fool people; we invite them to experience a different way of seeing the world.

## Creating Authentic Magical Moments

To create authentic magical moments, we must:

1. **Understand our audience** - Connect with their hopes, fears, and dreams
2. **Master our craft** - Technical skill is the foundation upon which artistry is built
3. **Embrace vulnerability** - True magic happens when we allow ourselves to be genuinely present
4. **Serve the mystery** - We are not the star; the impossible moment is

## The Responsibility of Wonder

With the power to create wonder comes great responsibility. We must:

- Never use our abilities to harm or deceive for personal gain
- Respect the intelligence and dignity of our audiences
- Preserve the sense of mystery that makes magic possible
- Pass on our knowledge to worthy successors

## Conclusion

Magic, at its highest form, is a spiritual practice that connects us to the fundamental mystery of existence. When we approach our art with reverence, skill, and genuine care for our audiences, we become part of an ancient tradition that has always sought to remind humanity that there is more to this world than meets the eye.

The next time you perform, ask yourself: Am I creating a trick, or am I creating magic? The answer will determine not just the quality of your performance, but the depth of its impact on those who witness it.`,
    excerpt: 'Magic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.',
    featured: true,
    published: true,
    author: 'Magic Academy',
    tags: ['philosophy', 'performance', 'wonder']
  },
  {
    title: 'The Lost Art of Misdirection: Classical Techniques for Modern Performers',
    slug: 'lost-art-misdirection',
    content: `# The Lost Art of Misdirection: Classical Techniques for Modern Performers

Misdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.

## Understanding True Misdirection

Misdirection is not about forcing attention away from something, but about naturally guiding it toward what you want the audience to see. The best misdirection feels completely natural and unforced.

### The Three Pillars of Classical Misdirection

1. **Physical Misdirection** - Using body language and movement
2. **Psychological Misdirection** - Leveraging human nature and expectations
3. **Temporal Misdirection** - Controlling the timing of attention

## Physical Misdirection Techniques

The masters understood that the human eye naturally follows movement, particularly the movement of the performer's eyes and hands.

### The Power of Eye Contact

Your eyes are your most powerful tool for misdirection:
- Where you look, the audience looks
- Breaking eye contact creates a moment of vulnerability that draws attention
- Sustained eye contact creates intimacy and trust

### Hand Positioning and Movement

Classical hand positioning follows these principles:
- Natural gestures feel invisible
- Unnatural gestures draw attention
- The hand that moves last is remembered most

## Psychological Misdirection

This is perhaps the most sophisticated form of misdirection, as it works with the audience's own thought processes.

### Expectation Management

People see what they expect to see. By carefully managing expectations, you can:
- Make the impossible seem logical
- Hide methods in plain sight
- Create surprise through violated expectations

### The Principle of Divided Attention

The human mind can only focus on one thing at a time. By giving the audience something interesting to think about, you can perform your method while their conscious mind is occupied.

## Temporal Misdirection

Timing is everything in misdirection. The classical approach emphasizes:

### The Beat

Every magical moment has a natural rhythm. Understanding this rhythm allows you to:
- Hide actions during moments of relaxed attention
- Build tension before the climax
- Create natural pauses that feel organic

### The Lag

There's always a slight delay between when something happens and when the audience processes it. Masters use this lag to their advantage.

## Modern Applications

These classical principles are more relevant than ever in our digital age:

- **Attention spans are shorter** - Classical misdirection helps maintain focus
- **Audiences are more skeptical** - Subtle techniques are less likely to be detected
- **Performance spaces vary** - These principles work in any environment

## Conclusion

The classical masters understood that true misdirection is an art form that requires years to master. It's not about fooling people, but about guiding them through an experience that feels natural and wonderful.

Study the classics, practice the fundamentals, and remember: the best misdirection is the kind that nobody notices.`,
    excerpt: 'Misdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.',
    featured: true,
    published: true,
    author: 'Magic Academy',
    tags: ['misdirection', 'technique', 'classical']
  },
  {
    title: 'The Psychology of Magic: Understanding Your Audience',
    slug: 'psychology-of-magic',
    content: `# The Psychology of Magic: Understanding Your Audience

Understanding the psychology of your audience is crucial for creating truly impactful magical experiences. This article explores the mental processes that occur during a magical performance and how to leverage them for maximum effect.

## The Spectator's Mind

When someone watches magic, their mind goes through several distinct phases:

1. **Observation** - Taking in the initial conditions
2. **Expectation** - Forming predictions about what will happen
3. **Surprise** - Experiencing the impossible moment
4. **Rationalization** - Attempting to explain what just occurred
5. **Wonder** - Accepting the mystery and enjoying the experience

## Cognitive Biases in Magic

Magicians can leverage several cognitive biases:

### Confirmation Bias
People tend to see what they expect to see. By setting up the right expectations, you can hide your methods in plain sight.

### The Availability Heuristic
Recent or memorable events seem more likely. This is why callbacks and running gags are so effective in magic.

### Inattentional Blindness
When focused on one thing, people miss obvious changes elsewhere. This is the foundation of many misdirection techniques.

## Building Emotional Connection

The most memorable magic happens when there's an emotional connection:

- **Personal relevance** - Make the magic about the spectator
- **Shared experience** - Create moments the audience experiences together
- **Vulnerability** - Show genuine emotion and humanity
- **Surprise** - Exceed expectations in unexpected ways

## Conclusion

Understanding psychology doesn't diminish the magic—it enhances it. When you understand how minds work, you can create experiences that are not just impossible, but deeply meaningful.`,
    excerpt: 'Understanding the psychology of your audience is crucial for creating truly impactful magical experiences. This article explores the mental processes that occur during a magical performance.',
    featured: false,
    published: true,
    author: 'Magic Academy',
    tags: ['psychology', 'audience', 'performance']
  },
  {
    title: 'The Renaissance of Card Magic: Why Classical Methods Still Matter',
    slug: 'renaissance-card-magic',
    content: `# The Renaissance of Card Magic: Why Classical Methods Still Matter

In an age of digital effects and high-tech illusions, the humble deck of cards remains one of the most powerful tools in a magician's arsenal. This piece examines why classical card techniques continue to captivate audiences and how they can be adapted for contemporary performance.

## The Timeless Appeal of Cards

Cards have several unique advantages:

- **Familiarity** - Everyone knows what cards are
- **Portability** - Always ready for impromptu performance
- **Versatility** - Endless possibilities for effects
- **Intimacy** - Perfect for close-up performance

## Classical Techniques That Endure

### The Double Lift
Still one of the most powerful moves in card magic, the double lift creates impossible transpositions and revelations.

### The Ambitious Card
This classic plot continues to amaze because it's simple, direct, and impossible.

### Card Controls
The ability to control cards while shuffling remains fundamental to advanced card work.

## Modern Adaptations

Today's performers are finding new ways to present classical methods:

- **Storytelling** - Wrapping techniques in compelling narratives
- **Technology integration** - Using phones and apps as magical props
- **Social media** - Creating effects designed for digital sharing

## The Future of Card Magic

Classical card magic isn't dying—it's evolving. The fundamental techniques remain the same, but the presentations continue to grow and adapt to new audiences and contexts.

## Conclusion

Master the classics first. Once you understand the fundamental principles, you'll be able to create new presentations that feel fresh while standing on the solid foundation of centuries of magical knowledge.`,
    excerpt: 'In an age of digital effects and high-tech illusions, the humble deck of cards remains one of the most powerful tools in a magician\'s arsenal. This piece examines why classical card techniques continue to captivate audiences.',
    featured: false,
    published: true,
    author: 'Magic Academy',
    tags: ['cards', 'classical', 'technique']
  },
  {
    title: 'Building Magical Moments: The Art of Presentation',
    slug: 'building-magical-moments',
    content: `# Building Magical Moments: The Art of Presentation

Technical skill is only half of magic—the other half is presentation. This article explores how to transform simple tricks into memorable magical moments through the power of presentation.

## The Elements of Presentation

### Premise
Every effect needs a clear, compelling reason for existing. Ask yourself: "Why am I doing this trick?"

### Structure
Good magic has a beginning, middle, and end:
- **Opening** - Establish the premise and engage the audience
- **Development** - Build tension and investment
- **Climax** - Deliver the impossible moment
- **Resolution** - Allow the audience to process and react

### Character
Who are you in this moment? The presentation should reflect your authentic personality while serving the effect.

## Creating Emotional Investment

The audience needs to care about what's happening:

- **Stakes** - What happens if the magic fails?
- **Personal connection** - How does this relate to the spectator?
- **Surprise** - Exceed their expectations
- **Wonder** - Leave them with questions

## The Power of Scripting

While you shouldn't sound scripted, having a clear structure helps:

1. **Hook** - Grab attention immediately
2. **Premise** - Explain what you're going to attempt
3. **Process** - Guide them through the experience
4. **Payoff** - Deliver the impossible moment
5. **Tag** - End with impact

## Adapting to Your Audience

Different audiences require different approaches:

- **Children** - High energy, visual effects, participation
- **Adults** - Sophisticated presentations, psychological elements
- **Corporate** - Professional demeanor, relevant themes
- **Intimate settings** - Personal connection, meaningful moments

## Conclusion

Great presentation turns tricks into experiences. Focus on creating moments that matter, and your magic will be remembered long after the method is forgotten.`,
    excerpt: 'Technical skill is only half of magic—the other half is presentation. This article explores how to transform simple tricks into memorable magical moments through the power of presentation.',
    featured: false,
    published: true,
    author: 'Magic Academy',
    tags: ['presentation', 'performance', 'audience']
  },
  {
    title: 'Preserving Magic History: The Importance of Studying the Masters',
    slug: 'preserving-magic-history',
    content: `# Preserving Magic History: The Importance of Studying the Masters

Every generation of magicians stands on the shoulders of those who came before. By studying the techniques, philosophies, and innovations of magical masters throughout history, we not only improve our own craft but help preserve the rich heritage of our art.

## Why Study the Masters?

### Technical Excellence
The masters developed techniques that have stood the test of time because they work. Their methods are refined through years of performance.

### Philosophical Depth
Great magicians weren't just technicians—they were thinkers who understood the deeper principles of their art.

### Historical Context
Understanding how magic has evolved helps us appreciate where we are and where we might go.

## Essential Masters to Study

### Robert-Houdin (1805-1871)
Known as the "Father of Modern Magic," he elevated magic from street performance to theatrical art.

### Dai Vernon (1894-1992)
"The Professor" revolutionized close-up magic and influenced generations of performers.

### Tommy Wonder (1953-2006)
A master of presentation who showed how to make magic meaningful and emotional.

### Juan Tamariz (1942-present)
The Spanish master who combines technical excellence with joyful, chaotic presentation.

## Learning from the Past

### Study Their Methods
Don't just learn their tricks—understand their thinking process.

### Analyze Their Presentations
How did they structure their performances? What made them compelling?

### Understand Their Context
What challenges did they face? How did they adapt to their times?

## Preserving for the Future

We have a responsibility to:

- **Document** current innovations and techniques
- **Teach** the next generation of magicians
- **Archive** important magical knowledge
- **Share** stories and wisdom from the masters

## Conclusion

Magic is a living tradition that connects us across centuries. By studying the masters, we honor their contributions while building the foundation for future innovations. The magic we create today will someday be studied by future generations—make it worthy of that attention.`,
    excerpt: 'Every generation of magicians stands on the shoulders of those who came before. By studying the techniques, philosophies, and innovations of magical masters throughout history, we not only improve our own craft but help preserve the rich heritage of our art.',
    featured: false,
    published: true,
    author: 'Magic Academy',
    tags: ['history', 'masters', 'tradition']
  }
]

const samplePortfolioItems = [
  {
    title: 'The Vanishing Card',
    description: 'A mesmerizing card trick that defies logic and captivates audiences with its elegant simplicity. This performance showcases the perfect blend of technical skill and theatrical presentation, creating a moment of pure wonder that lingers in the minds of spectators long after the final reveal.',
    image_url: '/images/portfolio/vanishing-card.jpg',
    category: 'close-up',
    featured: true,
    date: '2024-01-15'
  },
  {
    title: 'Levitation Mastery',
    description: 'An impossible levitation effect that creates a moment of pure wonder and disbelief. Objects float and dance in mid-air in this stunning routine that challenges the very laws of physics.',
    image_url: '/videos/portfolio/levitation.mp4',
    category: 'stage',
    featured: true,
    date: '2024-02-20'
  },
  {
    title: 'Mind Reading Revelation',
    description: 'A psychological masterpiece that demonstrates the power of mentalism and human connection. An impossible demonstration of mind reading with a borrowed deck of cards that leaves audiences questioning the nature of reality.',
    image_url: '/images/portfolio/mind-reading.jpg',
    category: 'mentalism',
    featured: true,
    date: '2024-03-10'
  },
  {
    title: 'The Phoenix Rising',
    description: 'A dramatic transformation effect that symbolizes rebirth and the eternal nature of magic. This performance combines fire, illusion, and storytelling to create an unforgettable theatrical experience.',
    image_url: '/videos/portfolio/phoenix.mp4',
    category: 'stage',
    featured: true,
    date: '2024-04-05'
  },
  {
    title: 'Silk Symphony',
    description: 'An elegant silk routine that flows like poetry in motion, combining grace with mystery. This performance demonstrates how simple props can create profound magical moments through skilled presentation.',
    image_url: '/images/portfolio/silk-symphony.jpg',
    category: 'close-up',
    featured: false,
    date: '2024-05-12'
  },
  {
    title: 'The Impossible Prediction',
    description: 'A stunning prediction effect that challenges the very nature of time and causality. This mentalism routine creates a profound sense of wonder about the nature of reality itself.',
    image_url: '/videos/portfolio/prediction.mp4',
    category: 'mentalism',
    featured: true,
    date: '2024-06-18'
  },
  {
    title: 'Ethereal Card Manipulation',
    description: 'A mesmerizing display of card manipulation that defies the laws of physics. Cards appear, vanish, and transform in ways that seem to transcend the boundaries of the possible.',
    image_url: '/videos/portfolio/ethereal-cards.mp4',
    category: 'close-up',
    featured: false,
    date: '2024-07-01'
  },
  {
    title: 'Corporate Event - Tech Conference 2024',
    description: 'Close-up magic performance for 500+ attendees at a major technology conference. Performed interactive card magic and mentalism effects during networking breaks, creating memorable moments that enhanced the event experience.',
    image_url: '/images/portfolio/tech-conference.jpg',
    category: 'corporate',
    featured: false,
    date: '2024-03-15'
  },
  {
    title: 'Wedding Reception Magic',
    description: 'Intimate close-up performance creating magical moments for wedding guests during cocktail hour and reception. Specialized in romantic card magic and coin work that complemented the celebration atmosphere.',
    image_url: '/images/portfolio/wedding-magic.jpg',
    category: 'wedding',
    featured: false,
    date: '2024-02-20'
  },
  {
    title: 'Private Party Entertainment',
    description: 'Exclusive performance for a milestone birthday celebration. Combined stage magic with close-up work to entertain guests of all ages throughout the evening.',
    image_url: '/images/portfolio/private-party.jpg',
    category: 'private',
    featured: false,
    date: '2024-01-10'
  }
]

async function createTestUser() {
  try {
    console.log('Creating test user...')

    // First check if user already exists
    const { data: existingUsers } = await supabase.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === '<EMAIL>')

    if (existingUser) {
      console.log('✓ Test user already exists:', existingUser.email)
      return existingUser
    }

    // Create user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true, // Skip email confirmation
      user_metadata: {
        name: 'Demo User'
      }
    })

    if (authError) {
      console.error('Error creating auth user:', authError)
      return null
    }

    console.log('✓ Test user created successfully:', authData.user?.email)
    return authData.user
  } catch (error) {
    console.error('Error in createTestUser:', error)
    return null
  }
}

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...')

    // Clear existing data (be careful in production!)
    console.log('🧹 Clearing existing data...')
    await supabase.from('videos').delete().neq('id', '0')
    await supabase.from('purchases').delete().neq('id', '0')
    await supabase.from('tutorials').delete().neq('id', '0')
    await supabase.from('blog_posts').delete().neq('id', '0')
    await supabase.from('portfolio').delete().neq('id', '0')

    // Create test user first
    const testUser = await createTestUser()

    // Insert tutorials
    console.log('📚 Inserting tutorials...')
    const { error: tutorialsError } = await supabase
      .from('tutorials')
      .insert(sampleTutorials)

    if (tutorialsError) {
      console.error('❌ Error inserting tutorials:', tutorialsError)
      return
    }
    console.log('✓ Tutorials inserted successfully')

    // Insert videos
    console.log('🎥 Inserting videos...')
    const { error: videosError } = await supabase
      .from('videos')
      .insert(sampleVideos)

    if (videosError) {
      console.error('❌ Error inserting videos:', videosError)
      return
    }
    console.log('✓ Videos inserted successfully')

    // Insert blog posts
    console.log('📝 Inserting blog posts...')
    const { error: blogError } = await supabase
      .from('blog_posts')
      .insert(sampleBlogPosts)

    if (blogError) {
      console.error('❌ Error inserting blog posts:', blogError)
      return
    }
    console.log('✓ Blog posts inserted successfully')

    // Insert portfolio items
    console.log('🎨 Inserting portfolio items...')
    const { error: portfolioError } = await supabase
      .from('portfolio')
      .insert(samplePortfolioItems)

    if (portfolioError) {
      console.error('❌ Error inserting portfolio items:', portfolioError)
      return
    }
    console.log('✓ Portfolio items inserted successfully')

    // Create a sample purchase for the test user
    if (testUser) {
      console.log('🛒 Creating sample purchase for test user...')
      const { error: purchaseError } = await supabase
        .from('purchases')
        .insert([{
          id: 'purchase-1',
          user_id: testUser.id,
          tutorial_id: '1', // The Art of Card Control
          amount: 49.99,
          paypal_order_id: 'DEMO-ORDER-123',
          status: 'completed'
        }])

      if (purchaseError) {
        console.error('❌ Error creating sample purchase:', purchaseError)
      } else {
        console.log('✓ Sample purchase created successfully')
      }
    }

    console.log('\n🎉 Database seeding completed successfully!')
    console.log('\n📋 Test User Credentials:')
    console.log('Email: <EMAIL>')
    console.log('Password: password123')
    console.log('\n🚀 You can now log in to the application!')
  } catch (error) {
    console.error('❌ Error seeding database:', error)
  }
}

// Run the seeding function
seedDatabase()
