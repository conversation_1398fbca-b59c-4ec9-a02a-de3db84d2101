import { supabase } from './supabase'
import type { AppUser } from '@/types'
import type { User as SupabaseUser } from '@supabase/supabase-js'

export class SupabaseAuthService {
  static async signUp(email: string, password: string, name?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name || email.split('@')[0]
        }
      }
    })
    
    if (error) throw error
    return data
  }
  
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    return data
  }
  
  static async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }
  
  static async getCurrentUser(): Promise<AppUser | null> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) return null

    // 尝试获取用户资料，但不要因为失败而阻止认证
    let profile = null
    try {
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()
      profile = data
    } catch (error) {
      console.log('Profile not found, using auth user data only:', error)
    }

    return {
      id: user.id,
      email: user.email!,
      name: profile?.name || user.user_metadata?.name || user.email!.split('@')[0],
      createdAt: new Date(user.created_at),
      updatedAt: new Date(profile?.updated_at || user.updated_at)
    }
  }

  // 辅助方法：将 Supabase User 转换为 AppUser
  static mapSupabaseUserToAppUser(user: SupabaseUser, profile?: any): AppUser {
    return {
      id: user.id,
      email: user.email!,
      name: profile?.name || user.user_metadata?.name || user.email!.split('@')[0],
      createdAt: new Date(user.created_at),
      updatedAt: new Date(user.updated_at || user.created_at)
    }
  }
}