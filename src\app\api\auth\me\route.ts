import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAuthService } from '@/lib/auth-supabase'

export async function GET(request: NextRequest) {
  try {
    const user = await SupabaseAuthService.getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        }
      }
    })
  } catch (error: any) {
    console.error('Get current user API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get user information' 
      },
      { status: 500 }
    )
  }
}
