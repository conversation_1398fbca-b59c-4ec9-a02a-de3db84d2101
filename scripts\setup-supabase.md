# Supabase 设置指南

## 步骤 1: 获取服务角色密钥

1. 打开您的 Supabase 项目仪表板：https://supabase.com/dashboard
2. 选择您的项目：`talupaklhwduxpfgwaga`
3. 在左侧菜单中点击 "Settings" → "API"
4. 在 "Project API keys" 部分找到 "service_role" 密钥
5. 复制 `service_role` 密钥（不是 `anon` 密钥）

## 步骤 2: 添加服务角色密钥到环境变量

将以下行添加到您的 `.env.local` 文件中：

```
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## 步骤 3: 创建数据库架构

1. 在 Supabase 仪表板中，点击左侧菜单的 "SQL Editor"
2. 点击 "New query"
3. 复制 `supabase-schema.sql` 文件的内容并粘贴到查询编辑器中
4. 点击 "Run" 执行 SQL 脚本

## 步骤 4: 运行种子数据脚本

在项目根目录运行以下命令：

```bash
# 安装 ts-node（如果还没有安装）
npm install -g ts-node

# 运行种子数据脚本
npx ts-node scripts/seed-supabase.ts
```

## 步骤 5: 测试登录

脚本执行成功后，您可以使用以下凭据登录：

- **邮箱**: <EMAIL>
- **密码**: password123

## 预期结果

种子脚本将创建：

- ✅ 测试用户 (<EMAIL>)
- ✅ 3个示例教程
- ✅ 5个示例视频
- ✅ 2篇博客文章
- ✅ 2个作品集项目
- ✅ 1个示例购买记录（测试用户购买了第一个教程）

## 故障排除

### 错误：Missing required environment variables
- 确保您已将 `SUPABASE_SERVICE_ROLE_KEY` 添加到 `.env.local` 文件中

### 错误：relation "profiles" does not exist
- 确保您已在 Supabase 中运行了 `supabase-schema.sql` 脚本

### 错误：User already exists
- 如果测试用户已存在，脚本会跳过用户创建步骤

### 权限错误
- 确保您使用的是 `service_role` 密钥，而不是 `anon` 密钥
- `service_role` 密钥具有管理员权限，可以绕过 RLS 策略

## 安全提醒

⚠️ **重要**: `service_role` 密钥具有完全的数据库访问权限，请：
- 不要在客户端代码中使用它
- 不要将其提交到版本控制系统
- 仅在服务器端脚本中使用
- 确保 `.env.local` 文件在 `.gitignore` 中
