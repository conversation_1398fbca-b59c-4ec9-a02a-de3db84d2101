# Contributing to Magic Academy 🎩

Thank you for your interest in contributing to Magic Academy! This document provides guidelines and information for contributors.

## 🌟 Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different viewpoints and experiences

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- Git
- Basic knowledge of React, TypeScript, and Next.js

### Development Setup

1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/magic-academy.git`
3. Install dependencies: `npm install`
4. Set up environment variables (see README.md)
5. Run the development server: `npm run dev`

## 📝 Development Guidelines

### Code Style

We use ESLint and Prettier to maintain consistent code style:

```bash
# Check linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### TypeScript Guidelines

- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper type annotations
- Avoid `any` type unless absolutely necessary

```typescript
// ✅ Good
interface User {
  id: string
  email: string
  name?: string
}

const getUser = async (id: string): Promise<User | null> => {
  // implementation
}

// ❌ Bad
const getUser = async (id: any): Promise<any> => {
  // implementation
}
```

### Component Guidelines

#### File Structure
```
ComponentName/
├── index.tsx          # Main component
├── ComponentName.tsx  # Component implementation
├── types.ts          # Component-specific types
├── hooks.ts          # Component-specific hooks
└── __tests__/        # Tests
    └── ComponentName.test.tsx
```

#### Component Template
```typescript
/**
 * ComponentName - Brief description
 */

import React from 'react'
import { cn } from '@/lib/utils'

interface ComponentNameProps {
  className?: string
  children?: React.ReactNode
  // other props
}

export default function ComponentName({ 
  className,
  children,
  ...props 
}: ComponentNameProps) {
  return (
    <div className={cn('base-classes', className)} {...props}>
      {children}
    </div>
  )
}
```

### API Route Guidelines

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { validateInput, SecurityUtils } from '@/lib/validation'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = SecurityUtils.getClientIP(request)
    if (!SecurityUtils.checkRateLimit(clientIP)) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Input validation
    const data = await request.json()
    const validation = validateInput(data)
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      )
    }

    // Business logic
    const result = await performOperation(data)

    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('API Error:', error)
    
    const safeError = SecurityUtils.createSafeErrorResponse(
      error, 
      process.env.NODE_ENV === 'development'
    )
    
    return NextResponse.json(safeError, { status: 500 })
  }
}
```

### Database Guidelines

- Use the unified database service layer
- Implement proper error handling
- Use transactions for complex operations
- Add caching for frequently accessed data

```typescript
// ✅ Good
export class TutorialModel {
  async getById(id: string): Promise<Tutorial | null> {
    return PerformanceMonitor.time('TutorialModel.getById', async () => {
      // Check cache first
      const cacheKey = `tutorial:${id}`
      const cached = CacheManager.get<Tutorial>(cacheKey)
      if (cached) return cached

      // Database query
      const { data, error } = await supabase
        .from('tutorials')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      if (!data) return null

      const tutorial = this.mapToTutorial(data)
      CacheManager.set(cacheKey, tutorial, 600000) // 10 minutes
      
      return tutorial
    })
  }
}
```

## 🧪 Testing

### Writing Tests

- Write tests for all new features
- Aim for high test coverage (>80%)
- Use descriptive test names
- Test both happy path and error cases

```typescript
describe('TutorialModel', () => {
  describe('getById', () => {
    it('should return tutorial when found', async () => {
      // Arrange
      const mockTutorial = TestDataGenerator.generateTutorial()
      SupabaseMocker.mockTutorialQueries([mockTutorial])

      // Act
      const result = await tutorialModel.getById(mockTutorial.id)

      // Assert
      expect(result).toEqual(mockTutorial)
    })

    it('should return null when tutorial not found', async () => {
      // Arrange
      SupabaseMocker.mockTutorialQueries([])

      // Act
      const result = await tutorialModel.getById('non-existent-id')

      // Assert
      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      // Arrange
      SupabaseMocker.mockError('Database connection failed')

      // Act & Assert
      await expect(tutorialModel.getById('test-id'))
        .rejects.toThrow('Database connection failed')
    })
  })
})
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- TutorialModel.test.ts
```

## 📋 Pull Request Process

### Before Submitting

1. **Create a feature branch**: `git checkout -b feature/your-feature-name`
2. **Write tests**: Ensure your code is well-tested
3. **Run the test suite**: `npm test`
4. **Check linting**: `npm run lint`
5. **Update documentation**: If needed
6. **Test manually**: Verify your changes work as expected

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added new tests for new functionality
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

### Review Process

1. **Automated checks**: CI/CD pipeline runs tests and linting
2. **Code review**: Maintainers review your code
3. **Feedback**: Address any requested changes
4. **Approval**: Once approved, your PR will be merged

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Try to reproduce the bug
3. Gather relevant information

### Bug Report Template

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g., Windows 10]
- Browser: [e.g., Chrome 91]
- Node.js version: [e.g., 18.0.0]

**Additional Context**
Screenshots, logs, etc.
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Feature Description**
Clear description of the feature

**Problem Statement**
What problem does this solve?

**Proposed Solution**
How should this work?

**Alternatives Considered**
Other solutions you've considered

**Additional Context**
Mockups, examples, etc.
```

## 📚 Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🤝 Community

- **Discord**: [Join our community](https://discord.gg/magicacademy)
- **GitHub Discussions**: Use for questions and ideas
- **Issues**: Use for bug reports and feature requests

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Annual contributor highlights

Thank you for contributing to Magic Academy! 🎩✨
