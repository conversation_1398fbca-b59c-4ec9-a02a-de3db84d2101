/**
 * 输入验证和安全工具
 */

import { NextRequest } from 'next/server'

// 基础验证规则
export const ValidationRules = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  password: /^.{8,}$/, // 至少8位
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  price: /^\d+(\.\d{1,2})?$/, // 价格格式
  tutorialTitle: /^.{1,200}$/, // 标题长度限制
}

// 验证器接口
interface ValidationResult {
  isValid: boolean
  errors: string[]
}

// 通用验证器
export class Validator {
  private errors: string[] = []

  // 验证邮箱
  email(value: string, fieldName = 'Email'): this {
    if (!value) {
      this.errors.push(`${fieldName} is required`)
    } else if (!ValidationRules.email.test(value)) {
      this.errors.push(`${fieldName} format is invalid`)
    }
    return this
  }

  // 验证密码
  password(value: string, fieldName = 'Password'): this {
    if (!value) {
      this.errors.push(`${fieldName} is required`)
    } else if (!ValidationRules.password.test(value)) {
      this.errors.push(`${fieldName} must be at least 8 characters long`)
    }
    return this
  }

  // 验证UUID
  uuid(value: string, fieldName = 'ID'): this {
    if (!value) {
      this.errors.push(`${fieldName} is required`)
    } else if (!ValidationRules.uuid.test(value)) {
      this.errors.push(`${fieldName} format is invalid`)
    }
    return this
  }

  // 验证价格
  price(value: number | string, fieldName = 'Price'): this {
    if (value === undefined || value === null) {
      this.errors.push(`${fieldName} is required`)
    } else {
      const numValue = typeof value === 'string' ? parseFloat(value) : value
      if (isNaN(numValue) || numValue < 0) {
        this.errors.push(`${fieldName} must be a valid positive number`)
      }
    }
    return this
  }

  // 验证字符串长度
  stringLength(value: string, min: number, max: number, fieldName = 'Field'): this {
    if (!value) {
      this.errors.push(`${fieldName} is required`)
    } else if (value.length < min || value.length > max) {
      this.errors.push(`${fieldName} must be between ${min} and ${max} characters`)
    }
    return this
  }

  // 验证必填字段
  required(value: any, fieldName = 'Field'): this {
    if (value === undefined || value === null || value === '') {
      this.errors.push(`${fieldName} is required`)
    }
    return this
  }

  // 获取验证结果
  getResult(): ValidationResult {
    return {
      isValid: this.errors.length === 0,
      errors: [...this.errors]
    }
  }

  // 重置验证器
  reset(): this {
    this.errors = []
    return this
  }
}

// 创建新的验证器实例
export const createValidator = () => new Validator()

// 教程数据验证
export function validateTutorialData(data: any): ValidationResult {
  const validator = createValidator()

  validator
    .required(data.title, 'Title')
    .stringLength(data.title, 1, 200, 'Title')
    .required(data.description, 'Description')
    .stringLength(data.description, 1, 2000, 'Description')
    .price(data.price, 'Price')

  if (data.difficulty && !['beginner', 'intermediate', 'advanced'].includes(data.difficulty)) {
    validator.errors.push('Difficulty must be one of: beginner, intermediate, advanced')
  }

  return validator.getResult()
}

// 用户注册数据验证
export function validateRegistrationData(data: any): ValidationResult {
  const validator = createValidator()

  validator
    .email(data.email)
    .password(data.password)

  if (data.name) {
    validator.stringLength(data.name, 1, 100, 'Name')
  }

  return validator.getResult()
}

// 登录数据验证
export function validateLoginData(data: any): ValidationResult {
  const validator = createValidator()

  validator
    .email(data.email)
    .required(data.password, 'Password')

  return validator.getResult()
}

// 购买数据验证
export function validatePurchaseData(data: any): ValidationResult {
  const validator = createValidator()

  validator
    .uuid(data.tutorialId, 'Tutorial ID')
    .required(data.paypalOrderId, 'PayPal Order ID')
    .price(data.amount, 'Amount')

  return validator.getResult()
}

// 安全工具
export class SecurityUtils {
  // 清理用户输入，防止XSS
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return ''
    
    return input
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除javascript协议
      .replace(/on\w+=/gi, '') // 移除事件处理器
      .trim()
  }

  // 限制字符串长度
  static limitLength(input: string, maxLength: number): string {
    if (typeof input !== 'string') return ''
    return input.substring(0, maxLength)
  }

  // 检查请求频率（简单实现）
  private static requestCounts = new Map<string, { count: number; resetTime: number }>()

  static checkRateLimit(identifier: string, maxRequests = 100, windowMs = 60000): boolean {
    const now = Date.now()
    const record = this.requestCounts.get(identifier)

    if (!record || now > record.resetTime) {
      this.requestCounts.set(identifier, { count: 1, resetTime: now + windowMs })
      return true
    }

    if (record.count >= maxRequests) {
      return false
    }

    record.count++
    return true
  }

  // 获取客户端IP（用于速率限制）
  static getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    if (realIP) {
      return realIP
    }

    return 'unknown'
  }

  // 生成安全的错误响应（不泄露敏感信息）
  static createSafeErrorResponse(error: any, isDevelopment = false) {
    if (isDevelopment) {
      return {
        success: false,
        error: error.message || 'An error occurred',
        details: error.stack
      }
    }

    // 生产环境下的通用错误消息
    const safeErrors: { [key: string]: string } = {
      'duplicate key': 'Resource already exists',
      'not found': 'Resource not found',
      'unauthorized': 'Authentication required',
      'forbidden': 'Access denied',
      'validation': 'Invalid input data'
    }

    const errorMessage = error.message?.toLowerCase() || ''
    
    for (const [key, message] of Object.entries(safeErrors)) {
      if (errorMessage.includes(key)) {
        return {
          success: false,
          error: message
        }
      }
    }

    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

// 中间件辅助函数
export function withValidation<T>(
  validationFn: (data: any) => ValidationResult,
  handler: (data: T) => Promise<any>
) {
  return async (data: any) => {
    const validation = validationFn(data)
    
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
    }

    return handler(data)
  }
}

// 速率限制中间件
export function withRateLimit(
  maxRequests = 100,
  windowMs = 60000,
  handler: (request: NextRequest) => Promise<any>
) {
  return async (request: NextRequest) => {
    const clientIP = SecurityUtils.getClientIP(request)
    
    if (!SecurityUtils.checkRateLimit(clientIP, maxRequests, windowMs)) {
      throw new Error('Rate limit exceeded')
    }

    return handler(request)
  }
}
