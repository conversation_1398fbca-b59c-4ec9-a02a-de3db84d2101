/**
 * 性能优化工具
 */

import { NextRequest, NextResponse } from 'next/server'

// 缓存管理器
export class CacheManager {
  private static cache = new Map<string, { data: any; expiry: number }>()

  // 设置缓存
  static set(key: string, data: any, ttlMs = 300000): void { // 默认5分钟
    const expiry = Date.now() + ttlMs
    this.cache.set(key, { data, expiry })
  }

  // 获取缓存
  static get<T>(key: string): T | null {
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    if (Date.now() > cached.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data as T
  }

  // 删除缓存
  static delete(key: string): void {
    this.cache.delete(key)
  }

  // 清空所有缓存
  static clear(): void {
    this.cache.clear()
  }

  // 清理过期缓存
  static cleanup(): void {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now > value.expiry) {
        this.cache.delete(key)
      }
    }
  }

  // 获取缓存统计
  static getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 定期清理过期缓存
setInterval(() => {
  CacheManager.cleanup()
}, 60000) // 每分钟清理一次

// 缓存装饰器
export function withCache<T extends any[], R>(
  keyGenerator: (...args: T) => string,
  ttlMs = 300000
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: T): Promise<R> {
      const cacheKey = keyGenerator(...args)
      
      // 尝试从缓存获取
      const cached = CacheManager.get<R>(cacheKey)
      if (cached !== null) {
        console.log(`Cache hit for key: ${cacheKey}`)
        return cached
      }

      // 执行原方法
      const result = await method.apply(this, args)
      
      // 存储到缓存
      CacheManager.set(cacheKey, result, ttlMs)
      console.log(`Cache set for key: ${cacheKey}`)
      
      return result
    }
  }
}

// 数据库查询优化
export class QueryOptimizer {
  // 批量查询优化
  static async batchQuery<T>(
    queries: Array<() => Promise<T>>,
    batchSize = 5
  ): Promise<T[]> {
    const results: T[] = []
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize)
      const batchResults = await Promise.all(batch.map(query => query()))
      results.push(...batchResults)
    }
    
    return results
  }

  // 分页查询优化
  static getPaginationParams(request: NextRequest) {
    const url = new URL(request.url)
    const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'))
    const limit = Math.min(100, Math.max(1, parseInt(url.searchParams.get('limit') || '10')))
    const offset = (page - 1) * limit

    return { page, limit, offset }
  }

  // 构建分页响应
  static buildPaginatedResponse<T>(
    data: T[],
    total: number,
    page: number,
    limit: number
  ) {
    const totalPages = Math.ceil(total / limit)
    
    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }
}

// 响应优化
export class ResponseOptimizer {
  // 压缩响应
  static compress(data: any): NextResponse {
    const response = NextResponse.json(data)
    
    // 设置缓存头
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=60')
    
    // 设置压缩头
    response.headers.set('Content-Encoding', 'gzip')
    
    return response
  }

  // 设置缓存头
  static withCacheHeaders(response: NextResponse, maxAge = 300): NextResponse {
    response.headers.set('Cache-Control', `public, max-age=${maxAge}, stale-while-revalidate=60`)
    response.headers.set('ETag', `"${Date.now()}"`)
    
    return response
  }

  // 条件响应（304 Not Modified）
  static conditionalResponse(request: NextRequest, lastModified: Date): NextResponse | null {
    const ifModifiedSince = request.headers.get('if-modified-since')
    
    if (ifModifiedSince) {
      const clientDate = new Date(ifModifiedSince)
      if (clientDate >= lastModified) {
        return new NextResponse(null, { status: 304 })
      }
    }
    
    return null
  }
}

// 性能监控
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>()

  // 记录执行时间
  static time<T>(label: string, fn: () => Promise<T>): Promise<T> {
    return new Promise(async (resolve, reject) => {
      const start = performance.now()
      
      try {
        const result = await fn()
        const duration = performance.now() - start
        
        this.recordMetric(label, duration)
        console.log(`${label} took ${duration.toFixed(2)}ms`)
        
        resolve(result)
      } catch (error) {
        const duration = performance.now() - start
        this.recordMetric(`${label}_error`, duration)
        reject(error)
      }
    })
  }

  // 记录指标
  private static recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    
    const values = this.metrics.get(label)!
    values.push(value)
    
    // 只保留最近100个记录
    if (values.length > 100) {
      values.shift()
    }
  }

  // 获取性能统计
  static getStats(label?: string) {
    if (label) {
      const values = this.metrics.get(label) || []
      return this.calculateStats(values)
    }
    
    const stats: { [key: string]: any } = {}
    for (const [key, values] of this.metrics.entries()) {
      stats[key] = this.calculateStats(values)
    }
    
    return stats
  }

  private static calculateStats(values: number[]) {
    if (values.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0 }
    }
    
    const sum = values.reduce((a, b) => a + b, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)
    
    return {
      count: values.length,
      avg: parseFloat(avg.toFixed(2)),
      min: parseFloat(min.toFixed(2)),
      max: parseFloat(max.toFixed(2))
    }
  }
}

// 资源预加载优化
export class ResourceOptimizer {
  // 生成预加载链接
  static generatePreloadLinks(resources: Array<{ href: string; as: string; type?: string }>) {
    return resources.map(resource => {
      const link = `<${resource.href}>; rel=preload; as=${resource.as}`
      return resource.type ? `${link}; type=${resource.type}` : link
    }).join(', ')
  }

  // 图片优化参数
  static getOptimizedImageUrl(originalUrl: string, width?: number, quality = 80) {
    if (!originalUrl) return ''
    
    const url = new URL(originalUrl, 'https://example.com')
    
    if (width) {
      url.searchParams.set('w', width.toString())
    }
    
    url.searchParams.set('q', quality.toString())
    url.searchParams.set('f', 'webp')
    
    return url.toString()
  }
}

// 内存优化
export class MemoryOptimizer {
  // 弱引用缓存（自动垃圾回收）
  private static weakCache = new WeakMap()

  // 设置弱引用缓存
  static setWeak(key: object, value: any): void {
    this.weakCache.set(key, value)
  }

  // 获取弱引用缓存
  static getWeak(key: object): any {
    return this.weakCache.get(key)
  }

  // 内存使用监控
  static getMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage()
      return {
        rss: Math.round(usage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
        external: Math.round(usage.external / 1024 / 1024) // MB
      }
    }
    return null
  }
}

// 导出性能优化中间件
export function withPerformanceOptimization(handler: Function) {
  return async (request: NextRequest, ...args: any[]) => {
    const startTime = performance.now()
    
    try {
      // 检查条件请求
      const lastModified = new Date()
      const conditionalResponse = ResponseOptimizer.conditionalResponse(request, lastModified)
      if (conditionalResponse) {
        return conditionalResponse
      }

      // 执行处理器
      const result = await handler(request, ...args)
      
      // 记录性能指标
      const duration = performance.now() - startTime
      PerformanceMonitor.recordMetric('api_request', duration)
      
      // 优化响应
      if (result instanceof NextResponse) {
        return ResponseOptimizer.withCacheHeaders(result)
      }
      
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      PerformanceMonitor.recordMetric('api_error', duration)
      throw error
    }
  }
}
