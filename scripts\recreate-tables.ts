import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function recreateTables() {
  try {
    console.log('🔄 Recreating database tables...\n')

    // Drop existing tables in correct order (due to foreign key constraints)
    console.log('🗑️ Dropping existing tables...')
    
    const dropQueries = [
      'DROP TABLE IF EXISTS purchases CASCADE;',
      'DROP TABLE IF EXISTS videos CASCADE;',
      'DROP TABLE IF EXISTS tutorials CASCADE;',
      'DROP TABLE IF EXISTS blog_posts CASCADE;',
      'DROP TABLE IF EXISTS portfolio_items CASCADE;'
    ]

    for (const query of dropQueries) {
      const { error } = await supabase.rpc('exec_sql', { sql: query })
      if (error) {
        console.error('❌ Error dropping table:', error)
      }
    }

    console.log('✅ Tables dropped successfully\n')

    // Create new tables with correct schema
    console.log('🏗️ Creating new tables...')

    const createQueries = [
      // Tutorials table
      `CREATE TABLE tutorials (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        featured BOOLEAN DEFAULT FALSE,
        difficulty TEXT NOT NULL DEFAULT 'beginner' CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
        duration INTEGER NOT NULL DEFAULT 0,
        cover_image TEXT,
        preview_video_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,

      // Videos table
      `CREATE TABLE videos (
        id TEXT PRIMARY KEY,
        tutorial_id TEXT REFERENCES tutorials(id) ON DELETE CASCADE NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        duration INTEGER NOT NULL,
        video_url TEXT NOT NULL,
        thumbnail_url TEXT,
        order_index INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,

      // Purchases table
      `CREATE TABLE purchases (
        id TEXT PRIMARY KEY,
        user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
        tutorial_id TEXT REFERENCES tutorials(id) ON DELETE CASCADE NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        paypal_order_id TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,

      // Blog posts table
      `CREATE TABLE blog_posts (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        excerpt TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        featured_image TEXT,
        published BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,

      // Portfolio items table
      `CREATE TABLE portfolio_items (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        image_url TEXT NOT NULL,
        video_url TEXT,
        category TEXT NOT NULL,
        featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`
    ]

    for (const query of createQueries) {
      const { error } = await supabase.rpc('exec_sql', { sql: query })
      if (error) {
        console.error('❌ Error creating table:', error)
        console.error('Query:', query)
      } else {
        const tableName = query.match(/CREATE TABLE (\w+)/)?.[1]
        console.log(`✅ Created table: ${tableName}`)
      }
    }

    console.log('\n🎉 Database tables recreated successfully!')
    console.log('\n📝 Next steps:')
    console.log('1. Run the seed script: npm run seed')
    console.log('2. Test the application')

  } catch (error) {
    console.error('❌ Error recreating tables:', error)
  }
}

recreateTables()
