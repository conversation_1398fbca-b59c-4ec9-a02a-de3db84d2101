# Magic Academy 🎩

A comprehensive magic tutorial platform built with Next.js, Supabase, and modern web technologies.

## 🌟 Features

- **User Authentication**: Secure login/registration with Supabase Auth
- **Tutorial Management**: Browse, purchase, and watch magic tutorials
- **Video Streaming**: High-quality video playback with Mux integration
- **Payment Processing**: Secure payments via PayPal integration
- **Responsive Design**: Beautiful, mobile-first design with classical aesthetics
- **Admin Dashboard**: Content management for tutorials and users
- **Blog System**: Share magic tips and news
- **Portfolio Showcase**: Display magical performances

## 🚀 Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (Database, Auth, Storage)
- **Video**: Mux for video hosting and streaming
- **Payments**: PayPal SDK
- **Testing**: Jest, React Testing Library
- **Deployment**: Vercel (recommended)

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- PayPal developer account (for payments)
- Mux account (for video hosting)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/magic-academy.git
   cd magic-academy
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your environment variables:
   ```env
   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # PayPal
   NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id
   PAYPAL_CLIENT_SECRET=your_paypal_client_secret

   # Mux
   MUX_TOKEN_ID=your_mux_token_id
   MUX_TOKEN_SECRET=your_mux_token_secret

   # App
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=http://localhost:3000
   ```

4. **Set up the database**
   ```bash
   npm run db:setup
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js 14 App Router
│   ├── (auth)/            # Authentication pages
│   ├── api/               # API routes
│   ├── tutorials/         # Tutorial pages
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── forms/            # Form components
│   └── layout/           # Layout components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── database/         # Database models
│   ├── auth/             # Authentication utilities
│   └── validation/       # Input validation
├── types/                # TypeScript type definitions
└── styles/               # Global styles
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy!

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 📚 API Documentation

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Tutorials

- `GET /api/tutorials` - Get all tutorials
- `GET /api/tutorials/[id]` - Get tutorial by ID
- `POST /api/tutorials` - Create tutorial (admin)
- `PUT /api/tutorials/[id]` - Update tutorial (admin)
- `DELETE /api/tutorials/[id]` - Delete tutorial (admin)

### Purchases

- `GET /api/purchases` - Get user purchases
- `POST /api/purchases` - Create purchase
- `POST /api/purchases/verify` - Verify PayPal payment

## 🎨 Design System

The project uses a classical magic theme with the following color palette:

- **Cream**: `#F5F5DC` - Background and light elements
- **Burgundy**: `#800020` - Primary brand color
- **Navy**: `#000080` - Secondary color
- **Gold**: `#FFD700` - Accent color
- **Forest**: `#228B22` - Success states

### Typography

- **Headings**: Playfair Display (serif)
- **Body**: Inter (sans-serif)
- **Decorative**: Crimson Text (serif)

## 🔧 Development Guidelines

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Use meaningful variable and function names
- Write JSDoc comments for complex functions

### Component Guidelines

- Use functional components with hooks
- Implement proper error boundaries
- Include loading states and error handling
- Write tests for all components

### Database Guidelines

- Use the unified database service layer
- Implement proper error handling
- Use transactions for complex operations
- Cache frequently accessed data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Convention

Use conventional commits:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions/changes
- `chore:` - Maintenance tasks

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/magicacademy)
- 📖 Documentation: [docs.magicacademy.com](https://docs.magicacademy.com)

## 🙏 Acknowledgments

- Magic community for inspiration
- Open source contributors
- Beta testers and early users

---

Made with ❤️ and a touch of magic ✨
