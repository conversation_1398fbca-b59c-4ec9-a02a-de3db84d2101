import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAuthService } from '@/lib/auth-supabase'

export async function POST(request: NextRequest) {
  try {
    await SupabaseAuthService.signOut()

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    })
  } catch (error: any) {
    console.error('Logout API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Logout failed' 
      },
      { status: 500 }
    )
  }
}
